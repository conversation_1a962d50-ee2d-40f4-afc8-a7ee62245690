#!/usr/bin/env python3
"""
Simple GUI Launcher for Shotgun-Code Integration
Launches the GUI with proper error handling and environment setup.
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    # Check tkinter
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    # Check if workflow directory exists
    if not os.path.exists("workflow"):
        missing_deps.append("workflow directory")
    
    # Check if integration directory exists
    if not os.path.exists("integration"):
        missing_deps.append("integration directory")
    
    return missing_deps

def setup_environment():
    """Setup the Python path and environment."""
    # Add current directory to Python path
    if "." not in sys.path:
        sys.path.insert(0, ".")
    
    # Add workflow directory to Python path
    workflow_path = os.path.join(os.getcwd(), "workflow")
    if workflow_path not in sys.path:
        sys.path.insert(0, workflow_path)
    
    # Add integration directory to Python path
    integration_path = os.path.join(os.getcwd(), "integration")
    if integration_path not in sys.path:
        sys.path.insert(0, integration_path)

def show_error_dialog(title, message):
    """Show error dialog using tkinter."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    messagebox.showerror(title, message)
    root.destroy()

def show_info_dialog(title, message):
    """Show info dialog using tkinter."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    messagebox.showinfo(title, message)
    root.destroy()

def main():
    """Main entry point for simple GUI launcher."""
    print("🚀 Launching Shotgun-Code Integration GUI...")
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        error_msg = f"Missing dependencies: {', '.join(missing_deps)}\n\n"
        error_msg += "Please ensure:\n"
        error_msg += "1. You're running from the workflow project root directory\n"
        error_msg += "2. The shotgun integration is properly installed\n"
        error_msg += "3. Run: ./install_shotgun_integration.sh"
        
        print(f"❌ Error: {error_msg}")
        try:
            show_error_dialog("Missing Dependencies", error_msg)
        except:
            pass  # If tkinter fails, just print the error
        return 1
    
    # Setup environment
    setup_environment()
    
    try:
        # Import and launch GUI
        print("📦 Importing GUI components...")
        from gui_launcher import WorkflowGUI
        
        print("🖥️  Starting GUI application...")
        app = WorkflowGUI()
        
        # Show startup message
        print("✅ GUI launched successfully!")
        print("💡 If the GUI window doesn't appear, check your display settings.")
        
        # Run the application
        app.run()
        
        print("👋 GUI application closed.")
        return 0
        
    except ImportError as e:
        error_msg = f"Failed to import GUI components: {e}\n\n"
        error_msg += "This usually means:\n"
        error_msg += "1. The shotgun integration is not properly installed\n"
        error_msg += "2. Missing Python dependencies\n"
        error_msg += "3. Environment variables not set\n\n"
        error_msg += "Try running: ./install_shotgun_integration.sh"
        
        print(f"❌ Import Error: {error_msg}")
        try:
            show_error_dialog("Import Error", error_msg)
        except:
            pass
        return 1
        
    except Exception as e:
        error_msg = f"Unexpected error: {e}\n\n"
        error_msg += "Please check the console output for more details."
        
        print(f"❌ Unexpected Error: {error_msg}")
        logger.exception("Unexpected error in GUI launcher")
        try:
            show_error_dialog("Unexpected Error", error_msg)
        except:
            pass
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
