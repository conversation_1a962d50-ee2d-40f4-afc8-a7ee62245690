#!/bin/bash
# Shotgun-Code Integration Installation Script
# Installs and configures shotgun-code integration for the workflow system

set -e

echo "🚀 Shotgun-Code Integration Installation"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

# Check Go
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install Go 1.20+ first."
    echo "Visit: https://golang.org/dl/"
    exit 1
fi

GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
print_status "Go version $GO_VERSION detected"

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js LTS first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

NODE_VERSION=$(node --version)
print_status "Node.js $NODE_VERSION detected"

# Check Wails CLI
if ! command -v wails &> /dev/null; then
    print_warning "Wails CLI not found. Installing..."
    go install github.com/wailsapp/wails/v2/cmd/wails@latest

    # Add Go bin to PATH if not already there
    if [[ ":$PATH:" != *":$HOME/go/bin:"* ]]; then
        echo 'export PATH=$PATH:$HOME/go/bin' >> ~/.bashrc
        export PATH=$PATH:$HOME/go/bin
    fi

    if command -v wails &> /dev/null; then
        print_status "Wails CLI installed successfully"
    else
        print_error "Failed to install Wails CLI"
        exit 1
    fi
else
    WAILS_VERSION=$(wails version)
    print_status "Wails CLI detected: $WAILS_VERSION"
fi

# Clone shotgun-code if not exists
echo ""
echo "📥 Setting up shotgun-code..."

if [ ! -d "shotgun-code" ]; then
    print_info "Cloning shotgun-code repository..."
    git clone https://github.com/glebkudr/shotgun_code.git shotgun-code
    print_status "Shotgun-code cloned successfully"
else
    print_info "Shotgun-code directory already exists"
    cd shotgun-code
    print_info "Updating shotgun-code..."
    git pull origin main || print_warning "Failed to update shotgun-code"
    cd ..
fi

# Setup shotgun-code
echo ""
echo "🔧 Building shotgun-code..."

cd shotgun-code

# Install Go dependencies
print_info "Installing Go dependencies..."
go mod tidy

# Install frontend dependencies
print_info "Installing frontend dependencies..."
cd frontend
npm install
cd ..

# Build shotgun-code
print_info "Building shotgun-code..."
wails build

# Check if build was successful
if [ -f "build/bin/shotgun-code" ] || [ -f "build/bin/shotgun-code.exe" ] || [ -f "build/bin/shotgun_code" ] || [ -f "build/bin/shotgun_code.exe" ]; then
    print_status "Shotgun-code built successfully"
else
    print_error "Failed to build shotgun-code"
    exit 1
fi

cd ..

# Install Python dependencies for integration
echo ""
echo "🐍 Installing Python integration dependencies..."

# Check if we're in a virtual environment
if [[ "$VIRTUAL_ENV" != "" ]]; then
    print_status "Virtual environment detected: $VIRTUAL_ENV"
else
    print_warning "Not in a virtual environment. Consider creating one:"
    echo "  python -m venv venv"
    echo "  source venv/bin/activate  # Linux/Mac"
    echo "  venv\\Scripts\\activate     # Windows"
fi

# Install additional dependencies for GUI
pip install tkinter || print_warning "tkinter might already be installed"

# Create integration directory if it doesn't exist
mkdir -p integration

# Make scripts executable
chmod +x workflow/enhanced_main.py
chmod +x integration/gui_launcher.py
chmod +x integration/shotgun_bridge.py

# Create launcher scripts
echo ""
echo "📝 Creating launcher scripts..."

# Create enhanced workflow launcher
cat > launch_enhanced_workflow.sh << 'EOF'
#!/bin/bash
# Enhanced Workflow Launcher with Shotgun Integration

cd "$(dirname "$0")"
python workflow/enhanced_main.py "$@"
EOF

chmod +x launch_enhanced_workflow.sh

# Create GUI launcher
cat > launch_gui.sh << 'EOF'
#!/bin/bash
# GUI Launcher for Workflow System

cd "$(dirname "$0")"
python workflow/enhanced_main.py --gui
EOF

chmod +x launch_gui.sh

# Create Windows batch files
cat > launch_enhanced_workflow.bat << 'EOF'
@echo off
cd /d "%~dp0"
python workflow\enhanced_main.py %*
EOF

cat > launch_gui.bat << 'EOF'
@echo off
cd /d "%~dp0"
python workflow\enhanced_main.py --gui
EOF

print_status "Launcher scripts created"

# Create example configuration
echo ""
echo "📋 Creating example configuration..."

cat > example_shotgun_usage.md << 'EOF'
# Shotgun Integration Usage Examples

## Command Line Usage

### Basic Enhanced Workflow
```bash
./launch_enhanced_workflow.sh \
  --project-path ./my-project \
  --project-id owner/repo \
  --branch main \
  --query "Add dark mode toggle to the UI"
```

### GUI Mode
```bash
./launch_gui.sh
```

### Disable Shotgun Integration
```bash
./launch_enhanced_workflow.sh \
  --no-shotgun \
  --project-id owner/repo \
  --query "Fix login bug"
```

## Python API Usage

```python
from integration.shotgun_bridge import ShotgunBridge
from workflow.enhanced_context_builder import EnhancedContextBuilder

# Initialize bridge
bridge = ShotgunBridge()

# Create enhanced configuration
config = bridge.create_enhanced_workflow_config(
    project_path="./my-project",
    user_request="Add dark mode toggle"
)

# Use enhanced context builder
builder = EnhancedContextBuilder(use_shotgun=True)
context = builder.build_combined_context(user_input, repo_context)
```

## Benefits of Shotgun Integration

1. **Complete Codebase Context**: Get entire project structure in one shot
2. **Smart File Filtering**: Automatically exclude noise (node_modules, etc.)
3. **Enhanced Planning**: AI gets better context for more specific plans
4. **GUI Interface**: Visual project selection and context preview
5. **Template-Oriented Output**: Structured, parseable context format

## Troubleshooting

### Shotgun Build Issues
```bash
cd shotgun-code
wails doctor  # Check Wails installation
wails build   # Rebuild if needed
```

### GUI Issues
```bash
pip install tkinter  # Install GUI dependencies
```

### Path Issues
```bash
export PATH=$PATH:$HOME/go/bin  # Add Go bin to PATH
```
EOF

print_status "Example configuration created"

# Final setup verification
echo ""
echo "🔍 Verifying installation..."

# Check if shotgun executable exists
if [ -f "shotgun-code/build/bin/shotgun-code" ] || [ -f "shotgun-code/build/bin/shotgun-code.exe" ] || [ -f "shotgun-code/build/bin/shotgun_code" ] || [ -f "shotgun-code/build/bin/shotgun_code.exe" ]; then
    print_status "Shotgun executable found"
else
    print_error "Shotgun executable not found"
fi

# Check if Python files exist
if [ -f "workflow/enhanced_main.py" ] && [ -f "integration/shotgun_bridge.py" ]; then
    print_status "Integration files found"
else
    print_error "Integration files missing"
fi

# Check if launcher scripts exist
if [ -f "launch_enhanced_workflow.sh" ] && [ -f "launch_gui.sh" ]; then
    print_status "Launcher scripts created"
else
    print_error "Launcher scripts missing"
fi

echo ""
echo "🎉 Shotgun-Code Integration Installation Complete!"
echo ""
echo "📋 Next Steps:"
echo "1. Configure your environment variables (see config/local_config.sh)"
echo "2. Test the integration:"
echo "   ./launch_enhanced_workflow.sh --help"
echo "3. Launch GUI mode:"
echo "   ./launch_gui.sh"
echo "4. Read usage examples:"
echo "   cat example_shotgun_usage.md"
echo ""
echo "🔗 For more information:"
echo "   - Shotgun-Code: https://github.com/glebkudr/shotgun_code"
echo "   - Wails: https://wails.io/"
echo "   - Integration docs: docs/SHOTGUN_INTEGRATION.md"
echo ""
print_status "Installation completed successfully!"
