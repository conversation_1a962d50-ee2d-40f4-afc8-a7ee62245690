#!/usr/bin/env python3
"""
GUI Launcher for Workflow System with Shotgun Integration
Provides a desktop interface that combines workflow system with shotgun-code GUI.
"""

import os
import sys
import json
import subprocess
import threading
import time
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext

# Add workflow directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'workflow'))

# Import workflow components
try:
    from trigger import WorkflowTrigger
    from enhanced_context_builder import EnhancedContextBuilder
    from model_interface import OpenAIModelInterface
    WORKFLOW_AVAILABLE = True
except ImportError as e:
    logging.error(f"Workflow components not available: {e}")
    WORKFLOW_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GUILauncher")

class WorkflowGUI:
    """
    Desktop GUI for the workflow system with shotgun integration.
    """

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Workflow System with Shotgun Integration")
        self.root.geometry("1200x800")

        # Initialize components
        self.workflow_trigger = None
        self.context_builder = None
        self.model_interface = None
        self.shotgun_process = None

        if WORKFLOW_AVAILABLE:
            try:
                self.workflow_trigger = WorkflowTrigger()
                self.context_builder = EnhancedContextBuilder()

                # Try to initialize model interface, but don't fail if env vars are missing
                try:
                    self.model_interface = OpenAIModelInterface()
                    logger.info("Model interface initialized successfully")
                except Exception as model_error:
                    logger.warning(f"Model interface initialization failed: {model_error}")
                    self.model_interface = None

                logger.info("Workflow components initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing workflow components: {e}")
                # Don't fail completely, just log the error
                self.workflow_trigger = None
                self.context_builder = None
                self.model_interface = None
        # GUI state
        self.selected_project = tk.StringVar()
        self.selected_branch = tk.StringVar(value="main")
        self.user_query = tk.StringVar()
        self.project_path = tk.StringVar()

        # Setup GUI
        self.setup_gui()

    def setup_gui(self):
        """Setup the main GUI interface."""
        # Create notebook for tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Tab 0: Configuration (if needed)
        if not self.model_interface:
            self.setup_config_tab(notebook)

        # Tab 1: Project Setup
        self.setup_project_tab(notebook)

        # Tab 2: Shotgun Integration
        self.setup_shotgun_tab(notebook)

        # Tab 3: Workflow Execution
        self.setup_workflow_tab(notebook)

        # Tab 4: Results
        self.setup_results_tab(notebook)

    def setup_config_tab(self, notebook):
        """Setup configuration tab for missing environment variables."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="⚠️ Configuration")

        ttk.Label(frame, text="Configuration Required", font=("Arial", 14, "bold")).pack(pady=10)

        # Warning message
        warning_frame = ttk.Frame(frame)
        warning_frame.pack(fill=tk.X, padx=20, pady=10)

        warning_text = """
⚠️ Missing Configuration

Some environment variables are not set. The workflow system can still generate
enhanced context using shotgun-code, but cannot create AI implementation plans.

Required environment variables:
• OPENAI_API_BASE_URL - URL of your OpenAI-compatible API server
• OPENAI_API_KEY - API key (if required by your server)

Optional environment variables:
• GITEA_URL - Your Gitea instance URL
• GITEA_ACCESS_TOKEN - Gitea personal access token
• GITMCP_SERVER_URL - Gitea-MCP server URL
        """

        warning_label = tk.Label(warning_frame, text=warning_text, justify=tk.LEFT,
                                wraplength=600, bg="lightyellow", padx=10, pady=10)
        warning_label.pack(fill=tk.X)

        # Configuration form
        config_frame = ttk.LabelFrame(frame, text="Quick Configuration")
        config_frame.pack(fill=tk.X, padx=20, pady=10)

        # OpenAI API Base URL
        ttk.Label(config_frame, text="OpenAI API Base URL:").pack(anchor=tk.W, padx=10, pady=(10, 0))
        self.openai_url_var = tk.StringVar(value="http://localhost:8001/v1")
        ttk.Entry(config_frame, textvariable=self.openai_url_var, width=60).pack(fill=tk.X, padx=10, pady=5)

        # OpenAI API Key
        ttk.Label(config_frame, text="OpenAI API Key (optional):").pack(anchor=tk.W, padx=10, pady=(10, 0))
        self.openai_key_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.openai_key_var, width=60, show="*").pack(fill=tk.X, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(config_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Apply Configuration",
                  command=self.apply_configuration).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Skip (Context Only)",
                  command=self.skip_configuration).pack(side=tk.LEFT)

        # Instructions
        instructions_text = """
Instructions:
1. Set up your OpenAI-compatible API server (e.g., local LLM with vLLM, Ollama, or OpenAI API)
2. Enter the API base URL above
3. Click "Apply Configuration" to enable full workflow functionality
4. Or click "Skip" to use context generation only
        """

        ttk.Label(frame, text=instructions_text, justify=tk.LEFT).pack(anchor=tk.W, padx=20, pady=10)

    def apply_configuration(self):
        """Apply the configuration and try to initialize model interface."""
        import os

        # Set environment variables
        os.environ["OPENAI_API_BASE_URL"] = self.openai_url_var.get()
        if self.openai_key_var.get():
            os.environ["OPENAI_API_KEY"] = self.openai_key_var.get()

        # Try to initialize model interface
        try:
            from model_interface import OpenAIModelInterface
            self.model_interface = OpenAIModelInterface()
            messagebox.showinfo("Success", "Configuration applied successfully! AI model interface is now available.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize model interface: {e}")

    def skip_configuration(self):
        """Skip configuration and continue with context-only mode."""
        messagebox.showinfo("Info", "Continuing in context-only mode. You can generate enhanced context but not AI implementation plans.")

    def setup_project_tab(self, notebook):
        """Setup project configuration tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Project Setup")

        # Project selection
        ttk.Label(frame, text="Project Configuration", font=("Arial", 14, "bold")).pack(pady=10)

        # Project path selection
        path_frame = ttk.Frame(frame)
        path_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(path_frame, text="Project Path:").pack(anchor=tk.W)
        path_entry_frame = ttk.Frame(path_frame)
        path_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(path_entry_frame, textvariable=self.project_path, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_entry_frame, text="Browse", command=self.browse_project_path).pack(side=tk.RIGHT, padx=(5, 0))

        # Project identifier
        id_frame = ttk.Frame(frame)
        id_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(id_frame, text="Project Identifier (owner/repo):").pack(anchor=tk.W)
        ttk.Entry(id_frame, textvariable=self.selected_project, width=40).pack(fill=tk.X, pady=5)

        # Branch selection
        branch_frame = ttk.Frame(frame)
        branch_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(branch_frame, text="Branch:").pack(anchor=tk.W)
        branch_combo = ttk.Combobox(branch_frame, textvariable=self.selected_branch,
                                   values=["main", "master", "develop", "dev"])
        branch_combo.pack(fill=tk.X, pady=5)

        # User query
        query_frame = ttk.Frame(frame)
        query_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        ttk.Label(query_frame, text="Your Request:").pack(anchor=tk.W)
        self.query_text = scrolledtext.ScrolledText(query_frame, height=8, wrap=tk.WORD)
        self.query_text.pack(fill=tk.BOTH, expand=True, pady=5)

        # Buttons
        button_frame = ttk.Frame(frame)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Validate Configuration",
                  command=self.validate_configuration).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Load Example",
                  command=self.load_example).pack(side=tk.LEFT)

    def setup_shotgun_tab(self, notebook):
        """Setup shotgun integration tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Shotgun Analysis")

        ttk.Label(frame, text="Shotgun-Code Integration", font=("Arial", 14, "bold")).pack(pady=10)

        # Shotgun controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Launch Shotgun GUI",
                  command=self.launch_shotgun_gui).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Generate Context",
                  command=self.generate_shotgun_context).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Stop Shotgun",
                  command=self.stop_shotgun).pack(side=tk.LEFT)

        # Context preview
        ttk.Label(frame, text="Generated Context Preview:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.context_preview = scrolledtext.ScrolledText(frame, height=20, wrap=tk.WORD)
        self.context_preview.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

    def setup_workflow_tab(self, notebook):
        """Setup workflow execution tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Workflow Execution")

        ttk.Label(frame, text="Workflow Execution", font=("Arial", 14, "bold")).pack(pady=10)

        # Execution controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Execute Workflow",
                  command=self.execute_workflow).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Clear Log",
                  command=self.clear_execution_log).pack(side=tk.LEFT)

        # Progress bar
        self.progress = ttk.Progressbar(frame, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=20, pady=10)

        # Execution log
        ttk.Label(frame, text="Execution Log:").pack(anchor=tk.W, padx=20, pady=(20, 5))
        self.execution_log = scrolledtext.ScrolledText(frame, height=20, wrap=tk.WORD)
        self.execution_log.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

    def setup_results_tab(self, notebook):
        """Setup results display tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Results")

        ttk.Label(frame, text="Generated Implementation Plan", font=("Arial", 14, "bold")).pack(pady=10)

        # Results controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Save Plan",
                  command=self.save_plan).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Export JSON",
                  command=self.export_json).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Clear Results",
                  command=self.clear_results).pack(side=tk.LEFT)

        # Results display
        self.results_display = scrolledtext.ScrolledText(frame, height=25, wrap=tk.WORD)
        self.results_display.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Store results data
        self.current_plan = None

    def browse_project_path(self):
        """Browse for project directory."""
        path = filedialog.askdirectory(title="Select Project Directory")
        if path:
            self.project_path.set(path)

    def validate_configuration(self):
        """Validate the current configuration."""
        errors = []

        if not self.selected_project.get():
            errors.append("Project identifier is required")

        if not self.selected_branch.get():
            errors.append("Branch is required")

        if not self.query_text.get("1.0", tk.END).strip():
            errors.append("User request is required")

        if errors:
            messagebox.showerror("Configuration Error", "\n".join(errors))
        else:
            messagebox.showinfo("Configuration Valid", "Configuration is valid!")

    def load_example(self):
        """Load example configuration."""
        self.selected_project.set("Forge/maestro")
        self.selected_branch.set("main")
        self.query_text.delete("1.0", tk.END)
        self.query_text.insert("1.0", "Add dark mode toggle to the UI with theme persistence")

    def launch_shotgun_gui(self):
        """Launch shotgun-code GUI."""
        try:
            # Find shotgun executable
            shotgun_path = self.find_shotgun_executable()
            if not shotgun_path:
                messagebox.showerror("Error", "Shotgun executable not found. Please build shotgun-code first.")
                return

            # Launch shotgun in development mode
            self.shotgun_process = subprocess.Popen([shotgun_path],
                                                   cwd=os.path.dirname(shotgun_path))

            self.log_message("Shotgun GUI launched successfully")
            messagebox.showinfo("Success", "Shotgun GUI launched! Use it to generate codebase context.")

        except Exception as e:
            self.log_message(f"Error launching shotgun: {e}")
            messagebox.showerror("Error", f"Failed to launch shotgun: {e}")

    def find_shotgun_executable(self):
        """Find shotgun executable."""
        possible_paths = [
            "../shotgun-code/build/bin/shotgun-code",
            "../shotgun-code/build/bin/shotgun-code.exe",
            "../shotgun-code/build/bin/shotgun_code",
            "../shotgun-code/build/bin/shotgun_code.exe",
            "./shotgun-code/build/bin/shotgun-code",
            "./shotgun-code/build/bin/shotgun-code.exe",
            "./shotgun-code/build/bin/shotgun_code",
            "./shotgun-code/build/bin/shotgun_code.exe"
        ]

        for path in possible_paths:
            if os.path.isfile(path) and os.access(path, os.X_OK):
                return os.path.abspath(path)
        return None

    def generate_shotgun_context(self):
        """Generate context using shotgun connector."""
        if not self.project_path.get():
            messagebox.showerror("Error", "Please select a project path first")
            return

        try:
            from shotgun_connector import ShotgunConnector

            connector = ShotgunConnector()
            context = connector.generate_codebase_context(
                repository_path=self.project_path.get(),
                exclude_patterns=["node_modules", ".git", "__pycache__"],
                include_patterns=["*.py", "*.js", "*.html", "*.css", "*.md"]
            )

            # Display context preview
            preview_text = f"Generation Method: {context.get('generation_method', 'unknown')}\n"
            preview_text += f"Total Files: {context['statistics']['total_files']}\n"
            preview_text += f"Total Size: {context['statistics']['total_size']} characters\n\n"
            preview_text += "Tree Structure:\n"
            preview_text += context.get('tree_structure', 'No tree structure available')[:2000]

            if len(context.get('tree_structure', '')) > 2000:
                preview_text += "\n... (truncated)"

            self.context_preview.delete("1.0", tk.END)
            self.context_preview.insert("1.0", preview_text)

            self.log_message(f"Generated context with {context['statistics']['total_files']} files")

        except Exception as e:
            self.log_message(f"Error generating context: {e}")
            messagebox.showerror("Error", f"Failed to generate context: {e}")

    def stop_shotgun(self):
        """Stop shotgun process."""
        if self.shotgun_process:
            self.shotgun_process.terminate()
            self.shotgun_process = None
            self.log_message("Shotgun process stopped")
        else:
            self.log_message("No shotgun process running")

    def execute_workflow(self):
        """Execute the complete workflow."""
        if not WORKFLOW_AVAILABLE:
            messagebox.showerror("Error", "Workflow components not available")
            return

        # Check if essential components are available
        if not self.context_builder:
            messagebox.showerror("Error", "Context builder not available. Please check your configuration.")
            return

        # Validate configuration first
        if not self.selected_project.get() or not self.selected_branch.get():
            messagebox.showerror("Error", "Please configure project and branch first")
            return

        user_query = self.query_text.get("1.0", tk.END).strip()
        if not user_query:
            messagebox.showerror("Error", "Please enter your request")
            return

        # Warn if model interface is not available
        if not self.model_interface:
            response = messagebox.askyesno(
                "Warning",
                "AI model interface is not available (missing environment variables).\n"
                "The workflow will generate context but cannot create implementation plans.\n"
                "Continue anyway?"
            )
            if not response:
                return

        # Start progress bar
        self.progress.start()

        # Execute workflow in separate thread
        thread = threading.Thread(target=self._execute_workflow_thread,
                                 args=(self.selected_project.get(),
                                      self.selected_branch.get(),
                                      user_query))
        thread.daemon = True
        thread.start()

    def _execute_workflow_thread(self, project_id, branch, query):
        """Execute workflow in separate thread."""
        try:
            self.log_message("Starting workflow execution...")

            # Prepare user input
            user_input = {
                "repository_identifier": project_id,
                "branch_name": branch,
                "user_query": query
            }

            # Mock repository context (in real implementation, this would come from Gitea)
            repository_context = {
                "project_url": f"http://localhost:3000/{project_id}",
                "branch": branch,
                "files": {},
                "message": "Mock context for GUI demo"
            }

            self.log_message("Building enhanced context...")

            # Build context
            combined_context = self.context_builder.build_combined_context(user_input, repository_context)

            # Generate plan if model interface is available
            plan = None
            if self.model_interface:
                self.log_message("Generating implementation plan...")
                plan = self.model_interface.generate_llm_template_and_send(combined_context)
            else:
                self.log_message("Skipping plan generation (model interface not available)")
                # Create a mock plan with the context information
                plan = {
                    "project_name": project_id,
                    "branch": branch,
                    "user_request_summary": query,
                    "overall_goal": f"Process request: {query}",
                    "context_generated": True,
                    "model_interface_available": False,
                    "context_statistics": {
                        "total_files": len(combined_context.get('project_files_context', {})),
                        "shotgun_enhanced": combined_context.get('shotgun_enhanced', False)
                    },
                    "message": "Context generated successfully. Configure OPENAI_API_BASE_URL to enable plan generation."
                }

            # Display results
            self.root.after(0, self._display_results, plan)

        except Exception as e:
            self.root.after(0, self._handle_workflow_error, str(e))
        finally:
            self.root.after(0, self.progress.stop)

    def _display_results(self, plan):
        """Display workflow results."""
        self.current_plan = plan

        if plan:
            # Format plan for display
            display_text = json.dumps(plan, indent=2, ensure_ascii=False)
            self.results_display.delete("1.0", tk.END)
            self.results_display.insert("1.0", display_text)

            self.log_message("Workflow completed successfully!")
            messagebox.showinfo("Success", "Implementation plan generated successfully!")
        else:
            self.log_message("Workflow failed to generate plan")
            messagebox.showerror("Error", "Failed to generate implementation plan")

    def _handle_workflow_error(self, error_msg):
        """Handle workflow execution error."""
        self.log_message(f"Workflow error: {error_msg}")
        messagebox.showerror("Workflow Error", f"Workflow execution failed: {error_msg}")

    def save_plan(self):
        """Save the current plan to file."""
        if not self.current_plan:
            messagebox.showerror("Error", "No plan to save")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.current_plan, f, indent=2, ensure_ascii=False)
                messagebox.showinfo("Success", f"Plan saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save plan: {e}")

    def export_json(self):
        """Export plan as JSON."""
        self.save_plan()  # Same functionality

    def clear_results(self):
        """Clear results display."""
        self.results_display.delete("1.0", tk.END)
        self.current_plan = None

    def clear_execution_log(self):
        """Clear execution log."""
        self.execution_log.delete("1.0", tk.END)

    def log_message(self, message):
        """Add message to execution log."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.execution_log.insert(tk.END, log_entry)
        self.execution_log.see(tk.END)

    def run(self):
        """Run the GUI application."""
        self.root.mainloop()

        # Cleanup
        if self.shotgun_process:
            self.shotgun_process.terminate()
        if self.context_builder:
            self.context_builder.cleanup()


def main():
    """Main entry point."""
    app = WorkflowGUI()
    app.run()


if __name__ == "__main__":
    main()
