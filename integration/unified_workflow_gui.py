#!/usr/bin/env python3
"""
Unified Workflow GUI - Shotgun-Enhanced Development Workflow
Combines Gitea repository management with shotgun-style context generation and AI planning.
"""

import os
import sys
import json
import threading
import time
import logging
import requests
from pathlib import Path
from typing import Dict, Any, Optional, List
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext

# Add workflow directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'workflow'))

# Import workflow components
try:
    from trigger import WorkflowTrigger
    from enhanced_context_builder import EnhancedContextBuilder
    from model_interface import OpenAIModelInterface
    from gitea_connector import GiteaMCPConnector
    from gitea_repo_manager import GiteaRepositoryManager
    WORKFLOW_AVAILABLE = True
except ImportError as e:
    logging.error(f"Workflow components not available: {e}")
    WORKFLOW_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("UnifiedWorkflowGUI")

class GiteaAuthDialog:
    """Dialog for Gitea authentication."""

    def __init__(self, parent):
        self.parent = parent
        self.result = None
        self.dialog = None

    def show(self):
        """Show the authentication dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("Gitea Authentication")
        self.dialog.geometry("500x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))

        self.setup_auth_dialog()

        # Wait for dialog to close
        self.parent.wait_window(self.dialog)
        return self.result

    def setup_auth_dialog(self):
        """Setup the authentication dialog UI."""
        # Title
        ttk.Label(self.dialog, text="Connect to Gitea",
                 font=("Arial", 14, "bold")).pack(pady=10)

        # Instructions
        instructions = """
Enter your Gitea server details to access repositories.
You can find your access token in Gitea Settings > Applications > Generate New Token.
        """
        ttk.Label(self.dialog, text=instructions, justify=tk.LEFT).pack(pady=10, padx=20)

        # Form frame
        form_frame = ttk.Frame(self.dialog)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Gitea URL
        ttk.Label(form_frame, text="Gitea URL:").pack(anchor=tk.W)
        self.url_var = tk.StringVar(value="http://172.16.20.246:3000")
        ttk.Entry(form_frame, textvariable=self.url_var, width=50).pack(fill=tk.X, pady=(0, 10))

        # Username
        ttk.Label(form_frame, text="Username:").pack(anchor=tk.W)
        self.username_var = tk.StringVar(value="Admin")
        ttk.Entry(form_frame, textvariable=self.username_var, width=50).pack(fill=tk.X, pady=(0, 10))

        # Access Token
        ttk.Label(form_frame, text="Access Token:").pack(anchor=tk.W)
        self.token_var = tk.StringVar(value="shotgun")
        ttk.Entry(form_frame, textvariable=self.token_var, width=50, show="*").pack(fill=tk.X, pady=(0, 10))

        # Test connection button
        ttk.Button(form_frame, text="Test Connection",
                  command=self.test_connection).pack(pady=10)

        # Status label
        self.status_label = ttk.Label(form_frame, text="", foreground="blue")
        self.status_label.pack(pady=5)

        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(button_frame, text="Connect",
                  command=self.connect).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="Cancel",
                  command=self.cancel).pack(side=tk.RIGHT)

    def test_connection(self):
        """Test the Gitea connection."""
        self.status_label.config(text="Testing connection...", foreground="blue")
        self.dialog.update()

        try:
            # Clean and validate inputs
            url = self.url_var.get().strip().rstrip('/')
            username = self.username_var.get().strip()
            token = self.token_var.get().strip()

            if not url or not username or not token:
                self.status_label.config(text="Please fill in all fields", foreground="red")
                return

            # Validate URL format
            if not url.startswith(('http://', 'https://')):
                self.status_label.config(text="URL must start with http:// or https://", foreground="red")
                return

            # Clean token of any invalid characters
            import re
            # Remove any whitespace, newlines, or other control characters
            clean_token = re.sub(r'[\s\r\n\t]', '', token)

            if clean_token != token:
                self.token_var.set(clean_token)
                token = clean_token
                self.status_label.config(text="Token cleaned of whitespace", foreground="orange")
                self.dialog.update()

            # Test API call
            headers = {
                "Authorization": f"token {token}",
                "Content-Type": "application/json"
            }

            print(f"Testing connection to: {url}")
            print(f"Using token: {token[:10]}...")

            response = requests.get(f"{url}/api/v1/user", headers=headers, timeout=10)

            print(f"Response status: {response.status_code}")
            print(f"Response headers: {dict(response.headers)}")

            if response.status_code == 200:
                user_data = response.json()
                username = user_data.get('login', 'Unknown')
                self.status_label.config(text=f"✅ Connected as {username}", foreground="green")
            elif response.status_code == 401:
                error_text = response.text
                if "user does not exist" in error_text:
                    self.status_label.config(text="❌ Token invalid: Generate new token in Gitea", foreground="red")
                    # Show detailed help
                    import tkinter.messagebox as mb
                    mb.showwarning("Token Invalid",
                        "The access token is invalid or expired.\n\n"
                        "To fix this:\n"
                        "1. Go to Gitea → Settings → Applications\n"
                        "2. Generate new token with 'repo' and 'read:user' permissions\n"
                        "3. Copy the new token and try again\n\n"
                        "See docs/GITEA_TROUBLESHOOTING.md for detailed help.")
                else:
                    self.status_label.config(text="❌ Authentication failed: Check credentials", foreground="red")
            elif response.status_code == 404:
                self.status_label.config(text="❌ API endpoint not found: Check URL", foreground="red")
            else:
                self.status_label.config(text=f"❌ Connection failed: HTTP {response.status_code}", foreground="red")
                print(f"Response text: {response.text}")

        except requests.exceptions.ConnectionError as e:
            self.status_label.config(text=f"❌ Cannot connect to server: {url}", foreground="red")
            print(f"Connection error: {e}")
        except requests.exceptions.Timeout as e:
            self.status_label.config(text="❌ Connection timeout", foreground="red")
            print(f"Timeout error: {e}")
        except requests.exceptions.RequestException as e:
            self.status_label.config(text=f"❌ Request error: {str(e)}", foreground="red")
            print(f"Request error: {e}")
        except Exception as e:
            self.status_label.config(text=f"❌ Error: {str(e)}", foreground="red")
            print(f"Unexpected error: {e}")
            import traceback
            traceback.print_exc()

    def connect(self):
        """Connect to Gitea."""
        # Clean and validate inputs
        url = self.url_var.get().strip().rstrip('/')
        username = self.username_var.get().strip()
        token = self.token_var.get().strip()

        # Clean token of any invalid characters
        import re
        clean_token = re.sub(r'[\s\r\n\t]', '', token)

        if not all([url, username, clean_token]):
            messagebox.showerror("Error", "Please fill in all fields")
            return

        self.result = {
            "url": url,
            "username": username,
            "token": clean_token
        }
        self.dialog.destroy()

    def cancel(self):
        """Cancel authentication."""
        self.result = None
        self.dialog.destroy()


class UnifiedWorkflowGUI:
    """
    Unified GUI that combines Gitea repository management with shotgun-enhanced workflow.
    """

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Unified Workflow System - Shotgun Enhanced")
        self.root.geometry("1400x900")

        # Initialize state
        self.gitea_config = None
        self.gitea_repos = []
        self.gitea_repo_manager = None
        self.selected_source = tk.StringVar(value="local")  # "local" or "gitea"
        self.selected_repo = tk.StringVar()
        self.selected_branch = tk.StringVar(value="main")
        self.local_path = tk.StringVar()

        # Initialize GUI component references
        self.local_section = None
        self.gitea_project_section = None
        self.gitea_section = None

        # Initialize workflow components
        self.workflow_trigger = None
        self.context_builder = None
        self.model_interface = None
        self.gitea_connector = None

        if WORKFLOW_AVAILABLE:
            try:
                self.workflow_trigger = WorkflowTrigger()
                self.context_builder = EnhancedContextBuilder(use_shotgun=True)

                # Try to initialize model interface
                try:
                    self.model_interface = OpenAIModelInterface()
                    logger.info("Model interface initialized successfully")
                except Exception as model_error:
                    logger.warning(f"Model interface initialization failed: {model_error}")
                    self.model_interface = None

                # Try to initialize Gitea connector
                try:
                    self.gitea_connector = GiteaMCPConnector()
                    logger.info("Gitea connector initialized successfully")
                except Exception as gitea_error:
                    logger.warning(f"Gitea connector initialization failed: {gitea_error}")
                    self.gitea_connector = None

                logger.info("Workflow components initialized successfully")
            except Exception as e:
                logger.error(f"Error initializing workflow components: {e}")

        # Setup GUI
        self.setup_gui()

    def setup_gui(self):
        """Setup the main GUI interface."""
        # Create main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Title
        title_frame = ttk.Frame(main_container)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(title_frame, text="Unified Workflow System",
                 font=("Arial", 16, "bold")).pack(side=tk.LEFT)

        # Configuration status
        self.config_status = ttk.Label(title_frame, text="⚠️ Configure AI Model",
                                      foreground="orange")
        if self.model_interface:
            self.config_status.config(text="✅ Ready", foreground="green")
        self.config_status.pack(side=tk.RIGHT)

        # Create notebook for tabs
        notebook = ttk.Notebook(main_container)
        notebook.pack(fill=tk.BOTH, expand=True)

        # Tab 1: Source Selection
        self.setup_source_tab(notebook)

        # Tab 2: Repository/Project Configuration
        self.setup_project_tab(notebook)

        # Tab 3: Context Generation
        self.setup_context_tab(notebook)

        # Tab 4: Workflow Execution
        self.setup_workflow_tab(notebook)

        # Tab 5: Results
        self.setup_results_tab(notebook)

        # Tab 6: Configuration (if needed)
        if not self.model_interface:
            self.setup_config_tab(notebook)

    def setup_source_tab(self, notebook):
        """Setup source selection tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="1. Source Selection")

        ttk.Label(frame, text="Choose Your Source", font=("Arial", 14, "bold")).pack(pady=10)

        # Source selection
        source_frame = ttk.LabelFrame(frame, text="Select Source Type")
        source_frame.pack(fill=tk.X, padx=20, pady=10)

        # Local folder option
        local_frame = ttk.Frame(source_frame)
        local_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Radiobutton(local_frame, text="Local Folder", variable=self.selected_source,
                       value="local", command=self.on_source_change).pack(side=tk.LEFT)

        ttk.Label(local_frame, text="Work with a local project directory").pack(side=tk.LEFT, padx=(10, 0))

        # Gitea option
        gitea_frame = ttk.Frame(source_frame)
        gitea_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Radiobutton(gitea_frame, text="Gitea Repository", variable=self.selected_source,
                       value="gitea", command=self.on_source_change).pack(side=tk.LEFT)

        ttk.Label(gitea_frame, text="Connect to Gitea and select from available repositories").pack(side=tk.LEFT, padx=(10, 0))

        # Gitea connection section
        self.gitea_section = ttk.LabelFrame(frame, text="Gitea Connection")
        self.gitea_section.pack(fill=tk.X, padx=20, pady=10)

        # Connection status
        self.gitea_status_frame = ttk.Frame(self.gitea_section)
        self.gitea_status_frame.pack(fill=tk.X, padx=10, pady=10)

        self.gitea_status_label = ttk.Label(self.gitea_status_frame, text="Not connected",
                                           foreground="red")
        self.gitea_status_label.pack(side=tk.LEFT)

        ttk.Button(self.gitea_status_frame, text="Connect to Gitea",
                  command=self.connect_to_gitea).pack(side=tk.RIGHT)

        # Initially disable gitea section (will be handled in setup_project_tab)

    def setup_project_tab(self, notebook):
        """Setup project configuration tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="2. Project Selection")

        ttk.Label(frame, text="Project Configuration", font=("Arial", 14, "bold")).pack(pady=10)

        # Local project section
        self.local_section = ttk.LabelFrame(frame, text="Local Project")
        self.local_section.pack(fill=tk.X, padx=20, pady=10)

        local_path_frame = ttk.Frame(self.local_section)
        local_path_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(local_path_frame, text="Project Path:").pack(anchor=tk.W)
        path_entry_frame = ttk.Frame(local_path_frame)
        path_entry_frame.pack(fill=tk.X, pady=5)

        ttk.Entry(path_entry_frame, textvariable=self.local_path, width=60).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(path_entry_frame, text="Browse", command=self.browse_local_path).pack(side=tk.RIGHT, padx=(5, 0))

        # Gitea project section
        self.gitea_project_section = ttk.LabelFrame(frame, text="Gitea Repository")
        self.gitea_project_section.pack(fill=tk.X, padx=20, pady=10)

        # Repository selection
        repo_frame = ttk.Frame(self.gitea_project_section)
        repo_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Label(repo_frame, text="Repository:").pack(anchor=tk.W)
        self.repo_combo = ttk.Combobox(repo_frame, textvariable=self.selected_repo,
                                      state="readonly", width=50)
        self.repo_combo.pack(fill=tk.X, pady=5)
        self.repo_combo.bind('<<ComboboxSelected>>', self.on_repo_selected)

        ttk.Button(repo_frame, text="Refresh Repositories",
                  command=self.refresh_repositories).pack(pady=5)

        # Branch selection (common for both)
        branch_frame = ttk.LabelFrame(frame, text="Branch Selection")
        branch_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Label(branch_frame, text="Branch:").pack(anchor=tk.W, padx=10)
        self.branch_combo = ttk.Combobox(branch_frame, textvariable=self.selected_branch,
                                        values=["main", "master", "develop", "dev"], width=30)
        self.branch_combo.pack(anchor=tk.W, padx=10, pady=5)

        # Add refresh branches button for Gitea repos
        ttk.Button(branch_frame, text="Refresh Branches",
                  command=self.refresh_branches).pack(anchor=tk.W, padx=10, pady=5)

        # User query
        query_frame = ttk.LabelFrame(frame, text="Development Request")
        query_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        ttk.Label(query_frame, text="Describe what you want to implement:").pack(anchor=tk.W, padx=10)
        self.query_text = scrolledtext.ScrolledText(query_frame, height=6, wrap=tk.WORD)
        self.query_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Example button
        ttk.Button(query_frame, text="Load Example",
                  command=self.load_example_query).pack(anchor=tk.W, padx=10, pady=5)

        # Now that all sections are created, set initial state
        self.on_source_change()

    def setup_context_tab(self, notebook):
        """Setup context generation tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="3. Context Generation")

        ttk.Label(frame, text="Shotgun-Enhanced Context Generation",
                 font=("Arial", 14, "bold")).pack(pady=10)

        # Controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Generate Context",
                  command=self.generate_context).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Clear Preview",
                  command=self.clear_context_preview).pack(side=tk.LEFT)

        # Progress bar
        self.context_progress = ttk.Progressbar(frame, mode='indeterminate')
        self.context_progress.pack(fill=tk.X, padx=20, pady=5)

        # Context preview
        preview_frame = ttk.LabelFrame(frame, text="Context Preview")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        self.context_preview = scrolledtext.ScrolledText(preview_frame, height=20, wrap=tk.WORD)
        self.context_preview.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Context statistics
        self.context_stats = ttk.Label(frame, text="No context generated yet")
        self.context_stats.pack(pady=5)

    def setup_workflow_tab(self, notebook):
        """Setup workflow execution tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="4. Workflow Execution")

        ttk.Label(frame, text="AI-Powered Implementation Planning",
                 font=("Arial", 14, "bold")).pack(pady=10)

        # Execution controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Generate Implementation Plan",
                  command=self.execute_workflow).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Clear Log",
                  command=self.clear_execution_log).pack(side=tk.LEFT)

        # Progress bar
        self.execution_progress = ttk.Progressbar(frame, mode='indeterminate')
        self.execution_progress.pack(fill=tk.X, padx=20, pady=5)

        # Execution log
        log_frame = ttk.LabelFrame(frame, text="Execution Log")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        self.execution_log = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.execution_log.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    def setup_results_tab(self, notebook):
        """Setup results display tab."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="5. Results")

        ttk.Label(frame, text="Implementation Plan", font=("Arial", 14, "bold")).pack(pady=10)

        # Results controls
        controls_frame = ttk.Frame(frame)
        controls_frame.pack(fill=tk.X, padx=20, pady=10)

        ttk.Button(controls_frame, text="Save Plan",
                  command=self.save_plan).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Export JSON",
                  command=self.export_json).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(controls_frame, text="Clear Results",
                  command=self.clear_results).pack(side=tk.LEFT)

        # Results display
        results_frame = ttk.LabelFrame(frame, text="Generated Implementation Plan")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        self.results_display = scrolledtext.ScrolledText(results_frame, height=20, wrap=tk.WORD)
        self.results_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Store results data
        self.current_plan = None
        self.current_context = None

    def setup_config_tab(self, notebook):
        """Setup configuration tab for missing environment variables."""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="⚠️ Configuration")

        ttk.Label(frame, text="AI Model Configuration", font=("Arial", 14, "bold")).pack(pady=10)

        # Warning message
        warning_frame = ttk.Frame(frame)
        warning_frame.pack(fill=tk.X, padx=20, pady=10)

        warning_text = """
⚠️ AI Model Not Configured

The AI model interface is not available. You can still generate enhanced context
using shotgun-style analysis, but cannot create implementation plans.

Required environment variables:
• OPENAI_API_BASE_URL - URL of your OpenAI-compatible API server
• OPENAI_API_KEY - API key (if required by your server)
        """

        warning_label = tk.Label(warning_frame, text=warning_text, justify=tk.LEFT,
                                wraplength=600, bg="lightyellow", padx=10, pady=10)
        warning_label.pack(fill=tk.X)

        # Configuration form
        config_frame = ttk.LabelFrame(frame, text="Quick Configuration")
        config_frame.pack(fill=tk.X, padx=20, pady=10)

        # OpenAI API Base URL
        ttk.Label(config_frame, text="OpenAI API Base URL:").pack(anchor=tk.W, padx=10, pady=(10, 0))
        self.openai_url_var = tk.StringVar(value="http://localhost:8001/v1")
        ttk.Entry(config_frame, textvariable=self.openai_url_var, width=60).pack(fill=tk.X, padx=10, pady=5)

        # OpenAI API Key
        ttk.Label(config_frame, text="OpenAI API Key (optional):").pack(anchor=tk.W, padx=10, pady=(10, 0))
        self.openai_key_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.openai_key_var, width=60, show="*").pack(fill=tk.X, padx=10, pady=5)

        # Buttons
        button_frame = ttk.Frame(config_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="Apply Configuration",
                  command=self.apply_ai_configuration).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Skip (Context Only)",
                  command=self.skip_ai_configuration).pack(side=tk.LEFT)

    def on_source_change(self):
        """Handle source type change."""
        # Only proceed if the sections have been created
        if not self.local_section or not self.gitea_project_section:
            return

        source = self.selected_source.get()

        if source == "local":
            # Enable local section, disable gitea section
            for child in self.local_section.winfo_children():
                self._enable_widget_tree(child)
            for child in self.gitea_project_section.winfo_children():
                self._disable_widget_tree(child)
        else:  # gitea
            # Enable gitea section, disable local section
            for child in self.gitea_project_section.winfo_children():
                self._enable_widget_tree(child)
            for child in self.local_section.winfo_children():
                self._disable_widget_tree(child)

    def _enable_widget_tree(self, widget):
        """Recursively enable widget and its children."""
        try:
            widget.configure(state='normal')
        except:
            pass
        for child in widget.winfo_children():
            self._enable_widget_tree(child)

    def _disable_widget_tree(self, widget):
        """Recursively disable widget and its children."""
        try:
            widget.configure(state='disabled')
        except:
            pass
        for child in widget.winfo_children():
            self._disable_widget_tree(child)

    def connect_to_gitea(self):
        """Show Gitea authentication dialog."""
        auth_dialog = GiteaAuthDialog(self.root)
        result = auth_dialog.show()

        if result:
            self.gitea_config = result
            self.gitea_status_label.config(text=f"✅ Connected to {result['url']}",
                                          foreground="green")

            # Set environment variables for gitea connector
            os.environ["GITEA_URL"] = result["url"]
            os.environ["GITEA_ACCESS_TOKEN"] = result["token"]

            # Initialize repository manager
            try:
                self.gitea_repo_manager = GiteaRepositoryManager(result)
                logger.info("Gitea repository manager initialized")
            except Exception as e:
                logger.error(f"Failed to initialize repository manager: {e}")
                messagebox.showerror("Error", f"Failed to initialize repository manager: {e}")
                return

            # Refresh repositories
            self.refresh_repositories()
        else:
            self.gitea_status_label.config(text="❌ Connection cancelled",
                                          foreground="red")

    def refresh_repositories(self):
        """Refresh the list of available repositories from Gitea."""
        if not self.gitea_repo_manager:
            messagebox.showerror("Error", "Please connect to Gitea first")
            return

        try:
            # Use repository manager to fetch repositories
            repos = self.gitea_repo_manager.get_repositories()

            if repos:
                repo_names = [f"{repo['owner']['login']}/{repo['name']}" for repo in repos]

                self.gitea_repos = repos
                self.repo_combo['values'] = repo_names

                if repo_names:
                    self.repo_combo.set(repo_names[0])
                    # Also refresh branches for the first repository
                    self.refresh_branches()

                messagebox.showinfo("Success", f"Found {len(repo_names)} repositories")
            else:
                messagebox.showwarning("Warning", "No repositories found or failed to fetch repositories")

        except Exception as e:
            messagebox.showerror("Error", f"Error fetching repositories: {e}")

    def refresh_branches(self):
        """Refresh branches for the selected repository."""
        if not self.gitea_repo_manager or not self.selected_repo.get():
            return

        try:
            repo_name = self.selected_repo.get()
            branches = self.gitea_repo_manager.get_repository_branches(repo_name)

            # Ensure branches is a list (handle None case)
            if branches is None:
                branches = ["main", "master"]
            elif not isinstance(branches, list):
                branches = ["main", "master"]

            # Update branch combo with fetched branches
            if hasattr(self, 'branch_combo'):
                self.branch_combo['values'] = branches
                if branches and self.selected_branch.get() not in branches:
                    self.selected_branch.set(branches[0])

        except Exception as e:
            logger.error(f"Error refreshing branches: {e}")
            # Set default branches on error
            if hasattr(self, 'branch_combo'):
                self.branch_combo['values'] = ["main", "master"]
                self.selected_branch.set("main")

    def on_repo_selected(self, event):
        """Handle repository selection change."""
        self.refresh_branches()

    def browse_local_path(self):
        """Browse for local project directory."""
        path = filedialog.askdirectory(title="Select Project Directory")
        if path:
            self.local_path.set(path)

    def load_example_query(self):
        """Load an example query."""
        example_queries = [
            "Add dark mode toggle to the UI with theme persistence and user preference storage",
            "Implement user authentication system with JWT tokens and password reset functionality",
            "Add real-time notifications using WebSocket connections with fallback to polling",
            "Create a REST API for user management with CRUD operations and validation",
            "Implement file upload functionality with progress tracking and drag-and-drop support",
            "Add search functionality with filters, pagination, and autocomplete suggestions"
        ]

        import random
        example = random.choice(example_queries)
        self.query_text.delete("1.0", tk.END)
        self.query_text.insert("1.0", example)

    def generate_context(self):
        """Generate shotgun-enhanced context."""
        # Validate inputs
        source = self.selected_source.get()

        if source == "local":
            if not self.local_path.get():
                messagebox.showerror("Error", "Please select a local project path")
                return
            project_path = self.local_path.get()
            project_id = f"local/{Path(project_path).name}"
        else:  # gitea
            if not self.selected_repo.get():
                messagebox.showerror("Error", "Please select a repository")
                return
            if not self.gitea_repo_manager:
                messagebox.showerror("Error", "Please connect to Gitea first")
                return

            project_id = self.selected_repo.get()
            branch = self.selected_branch.get()

            # Ask user whether to clone or use API
            use_clone = messagebox.askyesno(
                "Repository Access Method",
                f"How would you like to analyze {project_id}?\n\n"
                "Yes: Clone repository locally (more complete analysis)\n"
                "No: Use Gitea API (faster, but limited file access)"
            )

            if use_clone:
                # Clone the repository
                self.log_message(f"Cloning repository {project_id} (branch: {branch})...")
                project_path = self.gitea_repo_manager.clone_repository(project_id, branch)
                if not project_path:
                    messagebox.showerror("Error", f"Failed to clone repository {project_id}")
                    return
                self.log_message(f"Repository cloned to: {project_path}")
            else:
                # Use API-based context generation
                project_path = None  # Signal to use API method

        # Start progress bar
        self.context_progress.start()

        # Generate context in separate thread
        source = self.selected_source.get()
        branch = self.selected_branch.get() if source == "gitea" else "main"

        thread = threading.Thread(target=self._generate_context_thread,
                                 args=(project_path, project_id, source, branch))
        thread.daemon = True
        thread.start()

    def _generate_context_thread(self, project_path, project_id, source, branch):
        """Generate context in separate thread."""
        try:
            self.log_message("Starting shotgun-enhanced context generation...")

            if source == "gitea" and project_path is None:
                # Use Gitea API method
                if not self.gitea_repo_manager:
                    self.root.after(0, self._show_context_error, "Gitea repository manager not available")
                    return

                self.log_message("Using Gitea API for context generation...")
                context = self.gitea_repo_manager.get_repository_context_via_api(project_id, branch)

            else:
                # Use local shotgun-enhanced method
                if not self.context_builder:
                    self.root.after(0, self._show_context_error, "Context builder not available")
                    return

                self.log_message("Using local shotgun-enhanced context generation...")
                context = self.context_builder.context_builder.shotgun_connector.generate_codebase_context(
                    repository_path=project_path,
                    exclude_patterns=["node_modules", ".git", "__pycache__", "build", "dist"],
                    include_patterns=["*.py", "*.js", "*.ts", "*.html", "*.css", "*.md", "*.json"]
                )

            # Store context
            self.current_context = context

            # Display results
            self.root.after(0, self._display_context_results, context, project_id)

        except Exception as e:
            self.root.after(0, self._show_context_error, str(e))
        finally:
            self.root.after(0, self.context_progress.stop)

    def _display_context_results(self, context, project_id):
        """Display context generation results."""
        # Update preview
        preview_text = f"Project: {project_id}\n"
        preview_text += f"Generation Method: {context.get('generation_method', 'unknown')}\n"
        preview_text += f"Total Files: {context['statistics']['total_files']}\n"
        preview_text += f"Total Size: {context['statistics']['total_size']} characters\n"
        preview_text += f"File Types: {list(context['statistics']['file_types'].keys())}\n\n"

        preview_text += "Project Structure:\n"
        preview_text += "=" * 50 + "\n"
        preview_text += context.get('tree_structure', 'No tree structure available')[:3000]

        if len(context.get('tree_structure', '')) > 3000:
            preview_text += "\n... (truncated for display)"

        self.context_preview.delete("1.0", tk.END)
        self.context_preview.insert("1.0", preview_text)

        # Update statistics
        stats_text = f"✅ Context generated: {context['statistics']['total_files']} files, "
        stats_text += f"{context['statistics']['total_size']} chars, "
        stats_text += f"{len(context['statistics']['file_types'])} file types"
        self.context_stats.config(text=stats_text)

        self.log_message("Context generation completed successfully!")
        messagebox.showinfo("Success", "Shotgun-enhanced context generated successfully!")

    def _show_context_error(self, error_msg):
        """Show context generation error."""
        self.log_message(f"Context generation error: {error_msg}")
        messagebox.showerror("Error", f"Context generation failed: {error_msg}")

    def clear_context_preview(self):
        """Clear context preview."""
        self.context_preview.delete("1.0", tk.END)
        self.context_stats.config(text="No context generated yet")
        self.current_context = None

    def execute_workflow(self):
        """Execute the complete workflow to generate implementation plan."""
        # Validate that we have context
        if not self.current_context:
            messagebox.showerror("Error", "Please generate context first")
            return

        # Validate query
        user_query = self.query_text.get("1.0", tk.END).strip()
        if not user_query:
            messagebox.showerror("Error", "Please enter your development request")
            return

        # Check if model interface is available
        if not self.model_interface:
            response = messagebox.askyesno(
                "Warning",
                "AI model interface is not available. Cannot generate implementation plans.\n"
                "Would you like to configure it now?"
            )
            if response:
                # Switch to configuration tab
                return
            else:
                return

        # Start progress bar
        self.execution_progress.start()

        # Execute workflow in separate thread
        thread = threading.Thread(target=self._execute_workflow_thread, args=(user_query,))
        thread.daemon = True
        thread.start()

    def _execute_workflow_thread(self, user_query):
        """Execute workflow in separate thread."""
        try:
            self.log_message("Starting AI-powered implementation planning...")

            # Prepare user input
            source = self.selected_source.get()
            if source == "local":
                project_id = f"local/{Path(self.local_path.get()).name}"
            else:
                project_id = self.selected_repo.get()

            user_input = {
                "repository_identifier": project_id,
                "branch_name": self.selected_branch.get(),
                "user_query": user_query
            }

            # Prepare repository context from our generated context
            repository_context = {
                "project_url": f"local://{self.current_context['repository_path']}",
                "branch": self.selected_branch.get(),
                "files": self.current_context["files"],
                "message": "Context generated via shotgun-enhanced analysis"
            }

            self.log_message("Building combined context...")

            # Build combined context
            combined_context = self.context_builder.build_combined_context(
                user_input, repository_context
            )

            self.log_message("Generating implementation plan with AI...")

            # Generate plan
            plan = self.model_interface.generate_llm_template_and_send(combined_context)

            # Store and display results
            self.current_plan = plan
            self.root.after(0, self._display_workflow_results, plan)

        except Exception as e:
            self.root.after(0, self._show_workflow_error, str(e))
        finally:
            self.root.after(0, self.execution_progress.stop)

    def _display_workflow_results(self, plan):
        """Display workflow execution results."""
        if plan:
            # Format plan for display
            if isinstance(plan, dict):
                display_text = json.dumps(plan, indent=2, ensure_ascii=False)
            else:
                display_text = str(plan)

            self.results_display.delete("1.0", tk.END)
            self.results_display.insert("1.0", display_text)

            self.log_message("Implementation plan generated successfully!")

            # Show summary
            if isinstance(plan, dict) and "phases" in plan:
                phases = plan["phases"]
                summary = f"Generated plan with {len(phases)} implementation phases"
                messagebox.showinfo("Success", summary)
            else:
                messagebox.showinfo("Success", "Implementation plan generated successfully!")
        else:
            self.log_message("Failed to generate implementation plan")
            messagebox.showerror("Error", "Failed to generate implementation plan")

    def _show_workflow_error(self, error_msg):
        """Show workflow execution error."""
        self.log_message(f"Workflow error: {error_msg}")
        messagebox.showerror("Error", f"Workflow execution failed: {error_msg}")

    def apply_ai_configuration(self):
        """Apply AI model configuration."""
        import os

        # Set environment variables
        os.environ["OPENAI_API_BASE_URL"] = self.openai_url_var.get()
        if self.openai_key_var.get():
            os.environ["OPENAI_API_KEY"] = self.openai_key_var.get()

        # Try to initialize model interface
        try:
            from model_interface import OpenAIModelInterface
            self.model_interface = OpenAIModelInterface()
            self.config_status.config(text="✅ Ready", foreground="green")
            messagebox.showinfo("Success", "AI model configuration applied successfully!")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to initialize AI model: {e}")

    def skip_ai_configuration(self):
        """Skip AI model configuration."""
        messagebox.showinfo("Info", "Continuing without AI model. You can generate context but not implementation plans.")

    def save_plan(self):
        """Save the current plan to file."""
        if not self.current_plan:
            messagebox.showerror("Error", "No plan to save")
            return

        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("Text files", "*.txt"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    if filename.endswith('.json'):
                        json.dump(self.current_plan, f, indent=2, ensure_ascii=False)
                    else:
                        f.write(str(self.current_plan))
                messagebox.showinfo("Success", f"Plan saved to {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save plan: {e}")

    def export_json(self):
        """Export plan as JSON."""
        self.save_plan()

    def clear_results(self):
        """Clear results display."""
        self.results_display.delete("1.0", tk.END)
        self.current_plan = None

    def clear_execution_log(self):
        """Clear execution log."""
        self.execution_log.delete("1.0", tk.END)

    def log_message(self, message):
        """Add message to execution log."""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.execution_log.insert(tk.END, log_entry)
        self.execution_log.see(tk.END)
        self.root.update_idletasks()

    def run(self):
        """Run the GUI application."""
        self.root.mainloop()


def main():
    """Main entry point for unified workflow GUI."""
    app = UnifiedWorkflowGUI()
    app.run()


if __name__ == "__main__":
    main()
