#!/usr/bin/env python3
"""
Gitea Repository Manager
Handles cloning and managing Gitea repositories for the unified workflow system.
"""

import os
import sys
import json
import subprocess
import tempfile
import shutil
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("GiteaRepoManager")

class GiteaRepositoryManager:
    """
    Manages Gitea repository operations including cloning and context generation.
    """
    
    def __init__(self, gitea_config: Dict[str, str]):
        """
        Initialize the repository manager.
        
        Args:
            gitea_config: Dictionary with 'url', 'username', 'token'
        """
        self.gitea_config = gitea_config
        self.clone_dir = Path(tempfile.gettempdir()) / "workflow_gitea_clones"
        self.clone_dir.mkdir(exist_ok=True)
        
        logger.info(f"GiteaRepositoryManager initialized")
        logger.info(f"Clone directory: {self.clone_dir}")
    
    def get_repositories(self) -> List[Dict[str, Any]]:
        """
        Get list of repositories from Gitea.
        
        Returns:
            List of repository dictionaries
        """
        try:
            url = self.gitea_config["url"]
            token = self.gitea_config["token"]
            
            headers = {"Authorization": f"token {token}"}
            response = requests.get(f"{url}/api/v1/user/repos", headers=headers, timeout=30)
            
            if response.status_code == 200:
                repos = response.json()
                logger.info(f"Retrieved {len(repos)} repositories from Gitea")
                return repos
            else:
                logger.error(f"Failed to fetch repositories: {response.status_code}")
                return []
                
        except Exception as e:
            logger.error(f"Error fetching repositories: {e}")
            return []
    
    def get_repository_branches(self, repo_full_name: str) -> List[str]:
        """
        Get list of branches for a repository.
        
        Args:
            repo_full_name: Repository name in format "owner/repo"
            
        Returns:
            List of branch names
        """
        try:
            url = self.gitea_config["url"]
            token = self.gitea_config["token"]
            
            headers = {"Authorization": f"token {token}"}
            response = requests.get(
                f"{url}/api/v1/repos/{repo_full_name}/branches", 
                headers=headers, 
                timeout=30
            )
            
            if response.status_code == 200:
                branches = response.json()
                branch_names = [branch["name"] for branch in branches]
                logger.info(f"Retrieved {len(branch_names)} branches for {repo_full_name}")
                return branch_names
            else:
                logger.error(f"Failed to fetch branches for {repo_full_name}: {response.status_code}")
                return ["main", "master"]  # Default fallback
                
        except Exception as e:
            logger.error(f"Error fetching branches for {repo_full_name}: {e}")
            return ["main", "master"]  # Default fallback
    
    def clone_repository(self, repo_full_name: str, branch: str = "main") -> Optional[str]:
        """
        Clone a repository from Gitea.
        
        Args:
            repo_full_name: Repository name in format "owner/repo"
            branch: Branch to clone
            
        Returns:
            Path to cloned repository or None if failed
        """
        try:
            # Prepare clone path
            safe_name = repo_full_name.replace("/", "_")
            clone_path = self.clone_dir / f"{safe_name}_{branch}"
            
            # Remove existing clone if it exists
            if clone_path.exists():
                logger.info(f"Removing existing clone at {clone_path}")
                shutil.rmtree(clone_path)
            
            # Construct clone URL with authentication
            url = self.gitea_config["url"]
            username = self.gitea_config["username"]
            token = self.gitea_config["token"]
            
            # Use token authentication in URL
            clone_url = f"{url}/{repo_full_name}.git"
            auth_clone_url = clone_url.replace("://", f"://{username}:{token}@")
            
            logger.info(f"Cloning {repo_full_name} (branch: {branch}) to {clone_path}")
            
            # Clone the repository
            cmd = ["git", "clone", "-b", branch, "--depth", "1", auth_clone_url, str(clone_path)]
            
            # Run git clone with timeout
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=300,  # 5 minute timeout
                cwd=self.clone_dir
            )
            
            if result.returncode == 0:
                logger.info(f"Successfully cloned {repo_full_name} to {clone_path}")
                return str(clone_path)
            else:
                logger.error(f"Git clone failed: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            logger.error(f"Git clone timed out for {repo_full_name}")
            return None
        except Exception as e:
            logger.error(f"Error cloning repository {repo_full_name}: {e}")
            return None
    
    def get_repository_context_via_api(self, repo_full_name: str, branch: str = "main") -> Dict[str, Any]:
        """
        Get repository context via Gitea API (without cloning).
        
        Args:
            repo_full_name: Repository name in format "owner/repo"
            branch: Branch to analyze
            
        Returns:
            Repository context dictionary
        """
        try:
            url = self.gitea_config["url"]
            token = self.gitea_config["token"]
            headers = {"Authorization": f"token {token}"}
            
            context = {
                "repository_path": f"gitea://{repo_full_name}",
                "generation_method": "gitea-api",
                "tree_structure": "",
                "files": {},
                "statistics": {
                    "total_files": 0,
                    "total_size": 0,
                    "file_types": {}
                }
            }
            
            # Get repository tree
            tree_response = requests.get(
                f"{url}/api/v1/repos/{repo_full_name}/git/trees/{branch}?recursive=true",
                headers=headers,
                timeout=30
            )
            
            if tree_response.status_code != 200:
                logger.error(f"Failed to get repository tree: {tree_response.status_code}")
                return context
            
            tree_data = tree_response.json()
            tree_entries = tree_data.get("tree", [])
            
            # Filter for files only and common extensions
            include_extensions = {
                '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss',
                '.json', '.yaml', '.yml', '.md', '.txt', '.sh', '.go', '.rs'
            }
            
            exclude_patterns = {
                'node_modules', '.git', '__pycache__', 'build', 'dist',
                '.next', '.nuxt', 'coverage', 'vendor'
            }
            
            # Build tree structure and collect files
            tree_lines = [f"{repo_full_name}/"]
            
            for entry in tree_entries:
                if entry["type"] == "blob":  # File
                    file_path = entry["path"]
                    
                    # Skip excluded patterns
                    if any(pattern in file_path for pattern in exclude_patterns):
                        continue
                    
                    # Check file extension
                    file_ext = Path(file_path).suffix.lower()
                    if file_ext not in include_extensions:
                        continue
                    
                    # Add to tree structure
                    depth = file_path.count('/')
                    prefix = "│   " * depth
                    tree_lines.append(f"{prefix}├── {Path(file_path).name}")
                    
                    # Fetch file content
                    try:
                        file_response = requests.get(
                            f"{url}/api/v1/repos/{repo_full_name}/contents/{file_path}?ref={branch}",
                            headers=headers,
                            timeout=10
                        )
                        
                        if file_response.status_code == 200:
                            file_data = file_response.json()
                            if file_data.get("encoding") == "base64":
                                import base64
                                content = base64.b64decode(file_data["content"]).decode('utf-8', errors='ignore')
                                context["files"][file_path] = content
                                
                                # Update statistics
                                context["statistics"]["total_size"] += len(content)
                                context["statistics"]["file_types"][file_ext] = \
                                    context["statistics"]["file_types"].get(file_ext, 0) + 1
                    except Exception as file_error:
                        logger.warning(f"Failed to fetch file {file_path}: {file_error}")
                        continue
            
            context["tree_structure"] = '\n'.join(tree_lines)
            context["statistics"]["total_files"] = len(context["files"])
            
            logger.info(f"Generated API context for {repo_full_name}: {context['statistics']['total_files']} files")
            return context
            
        except Exception as e:
            logger.error(f"Error getting repository context via API: {e}")
            return context
    
    def cleanup_clones(self, keep_recent: int = 5):
        """
        Clean up old repository clones.
        
        Args:
            keep_recent: Number of recent clones to keep
        """
        try:
            if not self.clone_dir.exists():
                return
            
            # Get all clone directories sorted by modification time
            clone_dirs = [d for d in self.clone_dir.iterdir() if d.is_dir()]
            clone_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Remove old clones
            for old_clone in clone_dirs[keep_recent:]:
                logger.info(f"Removing old clone: {old_clone}")
                shutil.rmtree(old_clone)
                
        except Exception as e:
            logger.error(f"Error cleaning up clones: {e}")
    
    def get_clone_info(self) -> Dict[str, Any]:
        """
        Get information about current clones.
        
        Returns:
            Dictionary with clone information
        """
        try:
            if not self.clone_dir.exists():
                return {"clone_dir": str(self.clone_dir), "clones": []}
            
            clones = []
            for clone_path in self.clone_dir.iterdir():
                if clone_path.is_dir():
                    stat = clone_path.stat()
                    clones.append({
                        "name": clone_path.name,
                        "path": str(clone_path),
                        "size_mb": sum(f.stat().st_size for f in clone_path.rglob('*') if f.is_file()) / (1024 * 1024),
                        "modified": stat.st_mtime
                    })
            
            return {
                "clone_dir": str(self.clone_dir),
                "total_clones": len(clones),
                "clones": sorted(clones, key=lambda x: x["modified"], reverse=True)
            }
            
        except Exception as e:
            logger.error(f"Error getting clone info: {e}")
            return {"clone_dir": str(self.clone_dir), "clones": []}


# Example usage
if __name__ == "__main__":
    # Test configuration
    test_config = {
        "url": "http://localhost:3000",
        "username": "test_user",
        "token": "test_token"
    }
    
    manager = GiteaRepositoryManager(test_config)
    
    # Test getting repositories
    repos = manager.get_repositories()
    print(f"Found {len(repos)} repositories")
    
    # Test clone info
    clone_info = manager.get_clone_info()
    print(f"Clone info: {clone_info}")
    
    # Cleanup
    manager.cleanup_clones()
