#!/usr/bin/env python3
"""
Enhanced Workflow GUI with Multi-Repository Context
Extends the unified workflow GUI to include multi-repository context generation
for comprehensive code analysis and AI-assisted development.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from pathlib import Path
from typing import List, Dict, Any

# Import existing components
from unified_workflow_gui import UnifiedWorkflowGUI
from multi_repo_context_manager import MultiRepoContextManager, ProjectSource

logger = logging.getLogger("EnhancedWorkflowGUI")

class MultiRepoTab:
    """Tab for multi-repository context management."""
    
    def __init__(self, parent_notebook, workflow_gui):
        self.parent_notebook = parent_notebook
        self.workflow_gui = workflow_gui
        self.context_manager = MultiRepoContextManager()
        
        # Create the tab
        self.frame = ttk.Frame(parent_notebook)
        parent_notebook.add(self.frame, text="Multi-Repo Context")
        
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the multi-repo context UI."""
        # Main container with scrollbar
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Title
        title_label = ttk.Label(main_frame, text="Multi-Repository Context Generator", 
                               font=("Arial", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Description
        desc_text = ("Combine context from multiple local projects and repositories\n"
                    "to create comprehensive code examples for AI-assisted development.")
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.CENTER)
        desc_label.pack(pady=(0, 20))
        
        # Project Sources Section
        sources_frame = ttk.LabelFrame(main_frame, text="Project Sources")
        sources_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # Project list
        list_frame = ttk.Frame(sources_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Treeview for project sources
        columns = ("Name", "Type", "Path", "Priority", "Files")
        self.projects_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=120)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=scrollbar.set)
        
        self.projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons frame
        buttons_frame = ttk.Frame(sources_frame)
        buttons_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(buttons_frame, text="Add Local Project", 
                  command=self.add_local_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="Auto-Discover Projects", 
                  command=self.auto_discover_projects).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Remove Selected", 
                  command=self.remove_selected_project).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Clear All", 
                  command=self.clear_all_projects).pack(side=tk.LEFT, padx=5)
        
        # Target Project Section
        target_frame = ttk.LabelFrame(main_frame, text="Target Project (Optional)")
        target_frame.pack(fill=tk.X, pady=(0, 10))
        
        target_inner = ttk.Frame(target_frame)
        target_inner.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Label(target_inner, text="Target Project:").pack(side=tk.LEFT)
        self.target_var = tk.StringVar()
        self.target_combo = ttk.Combobox(target_inner, textvariable=self.target_var, 
                                        width=40, state="readonly")
        self.target_combo.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)
        
        # Generation Section
        generation_frame = ttk.LabelFrame(main_frame, text="Context Generation")
        generation_frame.pack(fill=tk.X, pady=(0, 10))
        
        gen_inner = ttk.Frame(generation_frame)
        gen_inner.pack(fill=tk.X, padx=10, pady=10)
        
        # Export format selection
        format_frame = ttk.Frame(gen_inner)
        format_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(format_frame, text="Export Format:").pack(side=tk.LEFT)
        self.format_var = tk.StringVar(value="markdown")
        for fmt in ["markdown", "json", "text"]:
            ttk.Radiobutton(format_frame, text=fmt.title(), variable=self.format_var, 
                           value=fmt).pack(side=tk.LEFT, padx=(10, 0))
        
        # Generate button
        self.generate_btn = ttk.Button(gen_inner, text="Generate Combined Context", 
                                      command=self.generate_context)
        self.generate_btn.pack(pady=10)
        
        # Progress bar
        self.progress = ttk.Progressbar(gen_inner, mode='indeterminate')
        self.progress.pack(fill=tk.X, pady=(0, 10))
        
        # Status/Results Section
        results_frame = ttk.LabelFrame(main_frame, text="Results")
        results_frame.pack(fill=tk.BOTH, expand=True)
        
        # Results text area
        results_inner = ttk.Frame(results_frame)
        results_inner.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.results_text = tk.Text(results_inner, height=8, wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_inner, orient=tk.VERTICAL, 
                                         command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Results buttons
        results_btn_frame = ttk.Frame(results_frame)
        results_btn_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(results_btn_frame, text="Open Output Folder", 
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(results_btn_frame, text="Copy to Clipboard", 
                  command=self.copy_to_clipboard).pack(side=tk.LEFT, padx=5)
        ttk.Button(results_btn_frame, text="Send to Workflow", 
                  command=self.send_to_workflow).pack(side=tk.LEFT, padx=5)
        
        self.output_folder = None
        
    def add_local_project(self):
        """Add a local project to the context manager."""
        folder_path = filedialog.askdirectory(title="Select Project Folder")
        if not folder_path:
            return
        
        # Simple dialog for project details
        dialog = ProjectDetailsDialog(self.frame, folder_path)
        if dialog.result:
            source = ProjectSource(
                name=dialog.result["name"],
                path=folder_path,
                type="local",
                description=dialog.result["description"],
                priority=dialog.result["priority"]
            )
            
            self.context_manager.add_project_source(source)
            self.refresh_projects_list()
            self.log_message(f"Added project: {source.name}")
    
    def auto_discover_projects(self):
        """Auto-discover projects in common directories."""
        base_dir = filedialog.askdirectory(title="Select Base Directory to Scan")
        if not base_dir:
            return
        
        self.log_message(f"Scanning {base_dir} for projects...")
        
        def discover_thread():
            try:
                discovered = self.context_manager.scan_directory_for_projects(base_dir, max_depth=2)
                
                # Add discovered projects
                for project in discovered:
                    self.context_manager.add_project_source(project)
                
                # Update UI in main thread
                self.frame.after(0, lambda: self._discovery_complete(len(discovered)))
                
            except Exception as e:
                self.frame.after(0, lambda: self.log_message(f"Error during discovery: {e}"))
        
        threading.Thread(target=discover_thread, daemon=True).start()
    
    def _discovery_complete(self, count):
        """Handle discovery completion."""
        self.refresh_projects_list()
        self.log_message(f"Auto-discovery complete: found {count} projects")
    
    def remove_selected_project(self):
        """Remove selected project from the list."""
        selection = self.projects_tree.selection()
        if not selection:
            messagebox.showwarning("Warning", "Please select a project to remove")
            return
        
        item = selection[0]
        project_name = self.projects_tree.item(item)["values"][0]
        
        # Remove from context manager
        self.context_manager.project_sources = [
            p for p in self.context_manager.project_sources if p.name != project_name
        ]
        
        self.refresh_projects_list()
        self.log_message(f"Removed project: {project_name}")
    
    def clear_all_projects(self):
        """Clear all projects."""
        if messagebox.askyesno("Confirm", "Remove all projects?"):
            self.context_manager.project_sources.clear()
            self.refresh_projects_list()
            self.log_message("Cleared all projects")
    
    def refresh_projects_list(self):
        """Refresh the projects list display."""
        # Clear existing items
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # Add current projects
        project_names = []
        for source in self.context_manager.project_sources:
            # Count files (simplified)
            try:
                file_count = len(list(Path(source.path).rglob("*"))) if Path(source.path).exists() else 0
            except:
                file_count = "?"
            
            self.projects_tree.insert("", tk.END, values=(
                source.name, source.type, source.path, source.priority, file_count
            ))
            project_names.append(source.name)
        
        # Update target combo
        self.target_combo["values"] = [""] + project_names
    
    def generate_context(self):
        """Generate combined context from all projects."""
        if not self.context_manager.project_sources:
            messagebox.showwarning("Warning", "Please add some projects first")
            return
        
        self.generate_btn.config(state="disabled")
        self.progress.start()
        self.log_message("Starting context generation...")
        
        def generation_thread():
            try:
                target_project = self.target_var.get() or None
                combined_context = self.context_manager.generate_combined_context(target_project)
                
                # Store context
                self.context_manager.combined_context = combined_context
                
                # Export in selected format
                output_dir = Path("multi_repo_context_output")
                output_dir.mkdir(exist_ok=True)
                
                format_type = self.format_var.get()
                output_file = output_dir / f"context.{format_type}"
                
                exported_file = self.context_manager.export_context_for_ai(
                    str(output_file), format=format_type
                )
                
                self.output_folder = output_dir
                
                # Update UI in main thread
                self.frame.after(0, lambda: self._generation_complete(combined_context, exported_file))
                
            except Exception as e:
                self.frame.after(0, lambda: self._generation_error(str(e)))
        
        threading.Thread(target=generation_thread, daemon=True).start()
    
    def _generation_complete(self, combined_context, exported_file):
        """Handle generation completion."""
        self.progress.stop()
        self.generate_btn.config(state="normal")
        
        stats = combined_context["combined_statistics"]
        
        result_text = f"""✅ Context Generation Complete!

📊 Statistics:
• Projects: {stats['total_projects']}
• Files: {stats['total_files']:,}
• Total Size: {stats['total_size']:,} characters
• Languages: {', '.join(stats['languages_detected'])}
• File Types: {len(stats['file_types'])} types

📁 Output: {exported_file}

🎯 Project Breakdown:
"""
        
        for project in combined_context["source_projects"]:
            result_text += f"• {project['name']} ({project['type']}): {project['file_count']} files\n"
        
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(1.0, result_text)
        
        self.log_message(f"Context exported to: {exported_file}")
    
    def _generation_error(self, error_msg):
        """Handle generation error."""
        self.progress.stop()
        self.generate_btn.config(state="normal")
        self.log_message(f"Error: {error_msg}")
        messagebox.showerror("Error", f"Context generation failed: {error_msg}")
    
    def open_output_folder(self):
        """Open the output folder."""
        if self.output_folder and self.output_folder.exists():
            import subprocess
            import platform
            
            if platform.system() == "Windows":
                subprocess.run(["explorer", str(self.output_folder)])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(self.output_folder)])
            else:  # Linux
                subprocess.run(["xdg-open", str(self.output_folder)])
        else:
            messagebox.showwarning("Warning", "No output folder available")
    
    def copy_to_clipboard(self):
        """Copy results to clipboard."""
        content = self.results_text.get(1.0, tk.END).strip()
        if content:
            self.frame.clipboard_clear()
            self.frame.clipboard_append(content)
            self.log_message("Results copied to clipboard")
        else:
            messagebox.showwarning("Warning", "No results to copy")
    
    def send_to_workflow(self):
        """Send context to the main workflow system."""
        if hasattr(self.context_manager, 'combined_context') and self.context_manager.combined_context:
            # This would integrate with the main workflow
            self.log_message("Context sent to workflow system")
            messagebox.showinfo("Success", "Context has been sent to the workflow system")
        else:
            messagebox.showwarning("Warning", "No context available to send")
    
    def log_message(self, message):
        """Log a message (delegate to main GUI)."""
        if hasattr(self.workflow_gui, 'log_message'):
            self.workflow_gui.log_message(f"[Multi-Repo] {message}")


class ProjectDetailsDialog:
    """Dialog for entering project details."""
    
    def __init__(self, parent, folder_path):
        self.result = None
        
        # Create dialog
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Project Details")
        self.dialog.geometry("400x300")
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")
        
        self.setup_ui(folder_path)
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def setup_ui(self, folder_path):
        """Setup dialog UI."""
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Project name
        ttk.Label(main_frame, text="Project Name:").pack(anchor=tk.W)
        self.name_var = tk.StringVar(value=Path(folder_path).name)
        ttk.Entry(main_frame, textvariable=self.name_var, width=50).pack(fill=tk.X, pady=(0, 10))
        
        # Description
        ttk.Label(main_frame, text="Description:").pack(anchor=tk.W)
        self.desc_text = tk.Text(main_frame, height=4, width=50)
        self.desc_text.pack(fill=tk.X, pady=(0, 10))
        
        # Priority
        ttk.Label(main_frame, text="Priority (1-10):").pack(anchor=tk.W)
        self.priority_var = tk.StringVar(value="5")
        ttk.Entry(main_frame, textvariable=self.priority_var, width=10).pack(anchor=tk.W, pady=(0, 10))
        
        # Path (read-only)
        ttk.Label(main_frame, text="Path:").pack(anchor=tk.W)
        path_label = ttk.Label(main_frame, text=folder_path, foreground="gray")
        path_label.pack(anchor=tk.W, pady=(0, 20))
        
        # Buttons
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)
        
        ttk.Button(btn_frame, text="OK", command=self.ok_clicked).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(btn_frame, text="Cancel", command=self.cancel_clicked).pack(side=tk.RIGHT)
    
    def ok_clicked(self):
        """Handle OK button click."""
        try:
            priority = int(self.priority_var.get())
            if not 1 <= priority <= 10:
                raise ValueError("Priority must be between 1 and 10")
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid priority: {e}")
            return
        
        self.result = {
            "name": self.name_var.get().strip(),
            "description": self.desc_text.get(1.0, tk.END).strip(),
            "priority": priority
        }
        
        if not self.result["name"]:
            messagebox.showerror("Error", "Project name is required")
            return
        
        self.dialog.destroy()
    
    def cancel_clicked(self):
        """Handle Cancel button click."""
        self.result = None
        self.dialog.destroy()


class EnhancedWorkflowGUI(UnifiedWorkflowGUI):
    """Enhanced workflow GUI with multi-repository context capabilities."""
    
    def __init__(self):
        super().__init__()
        self.root.title("Enhanced Workflow System - Multi-Repository Context")
        
        # Add multi-repo tab
        self.multi_repo_tab = MultiRepoTab(self.notebook, self)
        
        logger.info("Enhanced Workflow GUI initialized with multi-repo capabilities")


def main():
    """Launch the enhanced workflow GUI."""
    print("🚀 Launching Enhanced Workflow System with Multi-Repository Context...")
    
    try:
        app = EnhancedWorkflowGUI()
        app.root.mainloop()
    except Exception as e:
        print(f"❌ Error launching enhanced GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
