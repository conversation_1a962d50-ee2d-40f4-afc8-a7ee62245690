#!/usr/bin/env python3
"""
Multi-Repository Context Manager
Combines context from multiple local projects and repositories to create comprehensive
code examples and patterns for AI-assisted development.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
import fnmatch
from dataclasses import dataclass
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MultiRepoContextManager")

@dataclass
class ProjectSource:
    """Represents a project source for context generation."""
    name: str
    path: str
    type: str  # "local", "gitea", "git"
    description: str = ""
    priority: int = 1  # Higher priority = more important for context
    include_patterns: List[str] = None
    exclude_patterns: List[str] = None

class MultiRepoContextManager:
    """
    Manages context generation from multiple repositories and local projects.
    Creates a comprehensive codebase dump for AI analysis.
    """

    def __init__(self, cache_dir: Optional[str] = None):
        """
        Initialize the multi-repo context manager.

        Args:
            cache_dir: Directory for caching parsed contexts
        """
        self.cache_dir = Path(cache_dir or os.path.expanduser("~/.workflow_multi_repo_cache"))
        self.cache_dir.mkdir(exist_ok=True)

        self.project_sources: List[ProjectSource] = []
        self.combined_context: Dict[str, Any] = {}

        # Default file patterns
        self.default_include_patterns = [
            "*.py", "*.js", "*.ts", "*.jsx", "*.tsx", "*.go", "*.rs", "*.java",
            "*.c", "*.cpp", "*.h", "*.hpp", "*.php", "*.rb", "*.sql", "*.sh",
            "*.bat", "*.ps1", "*.yaml", "*.yml", "*.json", "*.toml", "*.ini",
            "*.md", "*.txt", "*.conf", "*.env", "*.dockerfile", "Dockerfile",
            "Makefile", "*.mk", "*.cmake", "CMakeLists.txt", "*.gradle",
            "*.xml", "*.html", "*.css", "*.scss", "*.sass", "*.vue", "*.svelte"
        ]

        self.default_exclude_patterns = [
            "node_modules/*", ".git/*", "__pycache__/*", "*.pyc", "*.pyo",
            ".venv/*", "venv/*", "env/*", "build/*", "dist/*", "target/*",
            ".next/*", ".nuxt/*", "coverage/*", ".nyc_output/*", ".pytest_cache/*",
            "vendor/*", ".gradle/*", ".mvn/*", "*.log", "*.tmp", "*.temp",
            ".DS_Store", "Thumbs.db", "*.swp", "*.swo", "*~", ".idea/*",
            ".vscode/*", "*.min.js", "*.min.css", "*.bundle.js", "*.chunk.js"
        ]

        logger.info(f"MultiRepoContextManager initialized with cache dir: {self.cache_dir}")

    def add_project_source(self, source: ProjectSource) -> None:
        """Add a project source for context generation."""
        # Validate path exists
        if not Path(source.path).exists():
            logger.warning(f"Project path does not exist: {source.path}")
            return

        # Set default patterns if not provided
        if source.include_patterns is None:
            source.include_patterns = self.default_include_patterns.copy()
        if source.exclude_patterns is None:
            source.exclude_patterns = self.default_exclude_patterns.copy()

        self.project_sources.append(source)
        logger.info(f"Added project source: {source.name} ({source.type}) at {source.path}")

    def add_local_project(self, name: str, path: str, description: str = "", priority: int = 1) -> None:
        """Convenience method to add a local project."""
        source = ProjectSource(
            name=name,
            path=path,
            type="local",
            description=description,
            priority=priority
        )
        self.add_project_source(source)

    def add_gitea_project(self, name: str, repo_path: str, description: str = "", priority: int = 1) -> None:
        """Convenience method to add a Gitea project (assumes already cloned)."""
        source = ProjectSource(
            name=name,
            path=repo_path,
            type="gitea",
            description=description,
            priority=priority
        )
        self.add_project_source(source)

    def scan_directory_for_projects(self, base_dir: str, max_depth: int = 2) -> List[ProjectSource]:
        """
        Automatically scan a directory for potential projects.

        Args:
            base_dir: Base directory to scan
            max_depth: Maximum depth to scan

        Returns:
            List of discovered project sources
        """
        discovered = []
        base_path = Path(base_dir)

        if not base_path.exists():
            logger.warning(f"Base directory does not exist: {base_dir}")
            return discovered

        # Project indicators
        project_indicators = [
            "package.json", "requirements.txt", "Cargo.toml", "go.mod", "pom.xml",
            "build.gradle", "composer.json", "Gemfile", "setup.py", "pyproject.toml",
            ".git", "main.py", "app.py", "index.js", "main.go", "src/"
        ]

        def scan_recursive(current_path: Path, depth: int = 0):
            if depth > max_depth:
                return

            try:
                for item in current_path.iterdir():
                    if item.is_dir() and not item.name.startswith('.'):
                        # Check if this directory looks like a project
                        has_indicators = any(
                            (item / indicator).exists() for indicator in project_indicators
                        )

                        if has_indicators:
                            # Determine project type
                            project_type = "local"
                            if (item / ".git").exists():
                                project_type = "git"

                            source = ProjectSource(
                                name=item.name,
                                path=str(item),
                                type=project_type,
                                description=f"Auto-discovered {project_type} project",
                                priority=1
                            )
                            discovered.append(source)
                            logger.info(f"Discovered project: {item.name} at {item}")
                        else:
                            # Continue scanning subdirectories
                            scan_recursive(item, depth + 1)
            except PermissionError:
                logger.warning(f"Permission denied accessing: {current_path}")

        scan_recursive(base_path)
        return discovered

    def generate_project_context(self, source: ProjectSource) -> Dict[str, Any]:
        """Generate context for a single project source."""
        logger.info(f"Generating context for project: {source.name}")

        context = {
            "project_name": source.name,
            "project_path": source.path,
            "project_type": source.type,
            "description": source.description,
            "priority": source.priority,
            "generation_timestamp": datetime.now().isoformat(),
            "tree_structure": "",
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {},
                "languages_detected": set()
            }
        }

        try:
            project_path = Path(source.path)
            if not project_path.exists():
                logger.error(f"Project path does not exist: {source.path}")
                return context

            # Build tree structure and collect files
            tree_lines = [f"{project_path.name}/"]

            def should_include_file(file_path: Path) -> bool:
                """Check if file should be included based on patterns."""
                relative_path = str(file_path.relative_to(project_path))

                # Check exclude patterns first
                for pattern in source.exclude_patterns:
                    if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(file_path.name, pattern):
                        return False

                # Check include patterns
                for pattern in source.include_patterns:
                    if fnmatch.fnmatch(relative_path, pattern) or fnmatch.fnmatch(file_path.name, pattern):
                        return True

                return False

            def build_tree_recursive(current_path: Path, prefix: str = "", is_last: bool = True):
                """Recursively build tree structure and collect files."""
                try:
                    entries = sorted(current_path.iterdir(), key=lambda x: (not x.is_dir(), x.name.lower()))
                    visible_entries = []

                    for entry in entries:
                        # Skip hidden files and excluded patterns
                        if entry.name.startswith('.') and entry.name not in {'.env', '.gitignore', '.dockerignore'}:
                            continue

                        # Check if directory should be excluded
                        relative_path = str(entry.relative_to(project_path))
                        excluded = any(
                            fnmatch.fnmatch(relative_path, pattern) for pattern in source.exclude_patterns
                        )
                        if excluded:
                            continue

                        visible_entries.append(entry)

                    for i, entry in enumerate(visible_entries):
                        is_entry_last = i == len(visible_entries) - 1
                        branch = "└── " if is_entry_last else "├── "
                        tree_lines.append(f"{prefix}{branch}{entry.name}")

                        if entry.is_dir():
                            next_prefix = prefix + ("    " if is_entry_last else "│   ")
                            build_tree_recursive(entry, next_prefix, is_entry_last)
                        elif should_include_file(entry):
                            # Add file to context
                            try:
                                relative_path = entry.relative_to(project_path)
                                with open(entry, 'r', encoding='utf-8', errors='ignore') as f:
                                    content = f.read()

                                context["files"][str(relative_path)] = content

                                # Update statistics
                                context["statistics"]["total_files"] += 1
                                context["statistics"]["total_size"] += len(content)

                                # Track file types
                                ext = entry.suffix.lower()
                                if ext:
                                    context["statistics"]["file_types"][ext] = \
                                        context["statistics"]["file_types"].get(ext, 0) + 1

                                # Detect languages
                                language = self._detect_language(entry)
                                if language:
                                    context["statistics"]["languages_detected"].add(language)

                            except (UnicodeDecodeError, PermissionError, OSError) as e:
                                logger.debug(f"Skipping file {entry}: {e}")
                                continue

                except (PermissionError, OSError) as e:
                    logger.warning(f"Cannot access directory {current_path}: {e}")

            # Build the tree and collect files
            build_tree_recursive(project_path)
            context["tree_structure"] = '\n'.join(tree_lines)

            # Convert set to list for JSON serialization
            context["statistics"]["languages_detected"] = list(context["statistics"]["languages_detected"])

            logger.info(f"Generated context for {source.name}: {context['statistics']['total_files']} files, "
                       f"{len(context['statistics']['languages_detected'])} languages")

            return context

        except Exception as e:
            logger.error(f"Error generating context for {source.name}: {e}")
            return context

    def _detect_language(self, file_path: Path) -> Optional[str]:
        """Detect programming language from file extension."""
        ext_to_lang = {
            '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
            '.jsx': 'React', '.tsx': 'React TypeScript', '.go': 'Go',
            '.rs': 'Rust', '.java': 'Java', '.c': 'C', '.cpp': 'C++',
            '.h': 'C Header', '.hpp': 'C++ Header', '.php': 'PHP',
            '.rb': 'Ruby', '.sql': 'SQL', '.sh': 'Shell', '.bat': 'Batch',
            '.ps1': 'PowerShell', '.yaml': 'YAML', '.yml': 'YAML',
            '.json': 'JSON', '.toml': 'TOML', '.ini': 'INI',
            '.html': 'HTML', '.css': 'CSS', '.scss': 'SCSS',
            '.sass': 'Sass', '.vue': 'Vue', '.svelte': 'Svelte'
        }
        return ext_to_lang.get(file_path.suffix.lower())

    def generate_combined_context(self, target_project: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate combined context from all project sources.

        Args:
            target_project: Name of the target project (gets highest priority)

        Returns:
            Combined context dictionary
        """
        logger.info(f"Generating combined context from {len(self.project_sources)} projects")

        combined = {
            "generation_timestamp": datetime.now().isoformat(),
            "target_project": target_project,
            "source_projects": [],
            "combined_statistics": {
                "total_projects": len(self.project_sources),
                "total_files": 0,
                "total_size": 0,
                "languages_detected": set(),
                "file_types": {},
                "projects_by_type": {}
            },
            "code_examples": {},
            "patterns": {},
            "project_contexts": {}
        }

        # Sort projects by priority (higher first), then by target project
        sorted_sources = sorted(
            self.project_sources,
            key=lambda x: (x.name == target_project, x.priority, x.name),
            reverse=True
        )

        for source in sorted_sources:
            try:
                # Generate context for this project
                project_context = self.generate_project_context(source)

                # Add to combined context
                combined["project_contexts"][source.name] = project_context
                combined["source_projects"].append({
                    "name": source.name,
                    "type": source.type,
                    "path": source.path,
                    "description": source.description,
                    "priority": source.priority,
                    "file_count": project_context["statistics"]["total_files"],
                    "languages": project_context["statistics"]["languages_detected"]
                })

                # Update combined statistics
                stats = combined["combined_statistics"]
                stats["total_files"] += project_context["statistics"]["total_files"]
                stats["total_size"] += project_context["statistics"]["total_size"]
                stats["languages_detected"].update(project_context["statistics"]["languages_detected"])

                # Merge file types
                for ext, count in project_context["statistics"]["file_types"].items():
                    stats["file_types"][ext] = stats["file_types"].get(ext, 0) + count

                # Track projects by type
                project_type = source.type
                stats["projects_by_type"][project_type] = stats["projects_by_type"].get(project_type, 0) + 1

                # Extract code examples and patterns
                self._extract_code_examples(source, project_context, combined)

            except Exception as e:
                logger.error(f"Error processing project {source.name}: {e}")
                continue

        # Convert sets to lists for JSON serialization
        combined["combined_statistics"]["languages_detected"] = list(combined["combined_statistics"]["languages_detected"])

        # Cache the combined context
        self._cache_combined_context(combined)

        logger.info(f"Combined context generated: {stats['total_files']} files from {stats['total_projects']} projects")
        return combined

    def _extract_code_examples(self, source: ProjectSource, project_context: Dict[str, Any], combined: Dict[str, Any]):
        """Extract useful code examples and patterns from a project."""
        examples = combined["code_examples"]
        patterns = combined["patterns"]

        # Group files by language/type for better organization
        for file_path, content in project_context["files"].items():
            file_path_obj = Path(file_path)
            language = self._detect_language(file_path_obj)

            if not language:
                continue

            # Initialize language section
            if language not in examples:
                examples[language] = {}
            if language not in patterns:
                patterns[language] = []

            # Categorize files
            category = self._categorize_file(file_path_obj, content)
            if category not in examples[language]:
                examples[language][category] = []

            # Add file example
            example = {
                "project": source.name,
                "file_path": file_path,
                "size": len(content),
                "content_preview": content[:500] + "..." if len(content) > 500 else content,
                "full_content": content
            }
            examples[language][category].append(example)

            # Extract patterns (function definitions, class definitions, etc.)
            file_patterns = self._extract_patterns_from_content(content, language)
            patterns[language].extend(file_patterns)

    def _categorize_file(self, file_path: Path, content: str) -> str:
        """Categorize a file based on its path and content."""
        path_str = str(file_path).lower()

        # Configuration files
        if any(keyword in path_str for keyword in ['config', 'settings', '.env', 'dockerfile']):
            return "configuration"

        # Test files
        if any(keyword in path_str for keyword in ['test', 'spec', '__test__']):
            return "tests"

        # Documentation
        if file_path.suffix.lower() in ['.md', '.txt', '.rst']:
            return "documentation"

        # API/Routes
        if any(keyword in path_str for keyword in ['api', 'route', 'endpoint', 'controller']):
            return "api"

        # Database/Models
        if any(keyword in path_str for keyword in ['model', 'schema', 'migration', 'db']):
            return "database"

        # UI/Frontend
        if any(keyword in path_str for keyword in ['component', 'view', 'template', 'ui']):
            return "frontend"

        # Utilities
        if any(keyword in path_str for keyword in ['util', 'helper', 'tool', 'lib']):
            return "utilities"

        # Main/Entry points
        if any(keyword in file_path.name.lower() for keyword in ['main', 'index', 'app', 'server']):
            return "entry_points"

        return "general"

    def _extract_patterns_from_content(self, content: str, language: str) -> List[Dict[str, Any]]:
        """Extract code patterns from file content."""
        patterns = []
        lines = content.split('\n')

        # Simple pattern extraction based on language
        if language == "Python":
            for i, line in enumerate(lines):
                stripped = line.strip()
                if stripped.startswith('def ') or stripped.startswith('class ') or stripped.startswith('async def '):
                    patterns.append({
                        "type": "definition",
                        "language": language,
                        "line": i + 1,
                        "content": stripped,
                        "context": '\n'.join(lines[max(0, i-2):i+5])  # Include some context
                    })

        elif language in ["JavaScript", "TypeScript"]:
            for i, line in enumerate(lines):
                stripped = line.strip()
                if any(keyword in stripped for keyword in ['function ', 'const ', 'class ', 'export ']):
                    patterns.append({
                        "type": "definition",
                        "language": language,
                        "line": i + 1,
                        "content": stripped,
                        "context": '\n'.join(lines[max(0, i-2):i+5])
                    })

        return patterns

    def _cache_combined_context(self, combined_context: Dict[str, Any]):
        """Cache the combined context to disk."""
        try:
            cache_file = self.cache_dir / f"combined_context_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            # Create a serializable version (remove full content to save space)
            cache_data = combined_context.copy()
            for project_name, project_context in cache_data["project_contexts"].items():
                # Keep only file paths and metadata, not full content
                project_context["files"] = {
                    path: {"size": len(content), "preview": content[:200]}
                    for path, content in project_context["files"].items()
                }

            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)

            logger.info(f"Combined context cached to: {cache_file}")

        except Exception as e:
            logger.error(f"Error caching combined context: {e}")

    def export_context_for_ai(self, output_file: str, format: str = "markdown") -> str:
        """
        Export combined context in AI-friendly format.

        Args:
            output_file: Output file path
            format: Export format ("markdown", "json", "text")

        Returns:
            Path to exported file
        """
        if not self.combined_context:
            logger.warning("No combined context available. Generate context first.")
            return ""

        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        if format == "markdown":
            self._export_markdown(output_path)
        elif format == "json":
            self._export_json(output_path)
        elif format == "text":
            self._export_text(output_path)
        else:
            raise ValueError(f"Unsupported format: {format}")

        logger.info(f"Context exported to: {output_path}")
        return str(output_path)

    def _export_markdown(self, output_path: Path):
        """Export context as markdown."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("# Multi-Repository Code Context\n\n")
            f.write(f"Generated: {self.combined_context['generation_timestamp']}\n\n")

            # Statistics
            stats = self.combined_context["combined_statistics"]
            f.write("## Overview\n\n")
            f.write(f"- **Total Projects**: {stats['total_projects']}\n")
            f.write(f"- **Total Files**: {stats['total_files']}\n")
            f.write(f"- **Languages**: {', '.join(stats['languages_detected'])}\n")
            f.write(f"- **File Types**: {', '.join(stats['file_types'].keys())}\n\n")

            # Project summaries
            f.write("## Projects\n\n")
            for project in self.combined_context["source_projects"]:
                f.write(f"### {project['name']} ({project['type']})\n")
                f.write(f"- **Path**: `{project['path']}`\n")
                f.write(f"- **Files**: {project['file_count']}\n")
                f.write(f"- **Languages**: {', '.join(project['languages'])}\n")
                if project['description']:
                    f.write(f"- **Description**: {project['description']}\n")
                f.write("\n")

            # Code examples by language
            f.write("## Code Examples\n\n")
            for language, categories in self.combined_context["code_examples"].items():
                f.write(f"### {language}\n\n")
                for category, examples in categories.items():
                    f.write(f"#### {category.title()}\n\n")
                    for example in examples[:3]:  # Limit to first 3 examples per category
                        f.write(f"**{example['project']}/{example['file_path']}**\n")
                        f.write(f"```{language.lower()}\n{example['content_preview']}\n```\n\n")

    def _export_json(self, output_path: Path):
        """Export context as JSON."""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.combined_context, f, indent=2, ensure_ascii=False)

    def _export_text(self, output_path: Path):
        """Export context as plain text."""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("MULTI-REPOSITORY CODE CONTEXT\n")
            f.write("=" * 50 + "\n\n")

            # Write all file contents
            for project_name, project_context in self.combined_context["project_contexts"].items():
                f.write(f"PROJECT: {project_name}\n")
                f.write("-" * 30 + "\n")

                for file_path, content in project_context["files"].items():
                    f.write(f"\nFILE: {file_path}\n")
                    f.write(f"{content}\n")
                    f.write("\n" + "=" * 80 + "\n")

    def cleanup(self):
        """Clean up resources."""
        logger.info("MultiRepoContextManager cleanup completed")
