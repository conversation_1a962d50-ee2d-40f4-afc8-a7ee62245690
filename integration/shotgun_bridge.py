#!/usr/bin/env python3
"""
Shotgun Bridge - Connects Workflow System with Shotgun-Code
Provides seamless integration between the two systems for enhanced context generation.
"""

import os
import sys
import json
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import xml.etree.ElementTree as ET

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ShotgunBridge")

class ShotgunBridge:
    """
    Bridge between workflow system and shotgun-code.
    Handles data transformation and communication between the systems.
    """

    def __init__(self, shotgun_path: Optional[str] = None, workflow_path: Optional[str] = None):
        """
        Initialize the bridge.

        Args:
            shotgun_path: Path to shotgun-code directory
            workflow_path: Path to workflow system directory
        """
        self.shotgun_path = shotgun_path or self._find_shotgun_path()
        self.workflow_path = workflow_path or self._find_workflow_path()
        self.temp_dir = tempfile.mkdtemp(prefix="shotgun_bridge_")

        logger.info(f"Bridge initialized:")
        logger.info(f"  Shotgun path: {self.shotgun_path}")
        logger.info(f"  Workflow path: {self.workflow_path}")
        logger.info(f"  Temp dir: {self.temp_dir}")

    def _find_shotgun_path(self) -> Optional[str]:
        """Find shotgun-code installation."""
        # Shotgun-code directory has been removed - using fallback mode only
        logger.info("Shotgun-code directory not available - using enhanced fallback mode")
        return None

    def _find_workflow_path(self) -> Optional[str]:
        """Find workflow system installation."""
        possible_paths = [
            "./workflow",
            "../workflow",
            "../../workflow",
            "."
        ]

        for path in possible_paths:
            if os.path.exists(os.path.join(path, "main.py")):
                return os.path.abspath(path)

        return None

    def create_enhanced_workflow_config(self, project_path: str, user_request: str) -> Dict[str, Any]:
        """
        Create enhanced workflow configuration using shotgun analysis.

        Args:
            project_path: Path to the project to analyze
            user_request: User's request/query

        Returns:
            Enhanced configuration dictionary
        """
        logger.info(f"Creating enhanced config for: {project_path}")

        try:
            # Generate shotgun context
            shotgun_context = self._generate_shotgun_context(project_path)

            # Analyze project structure
            project_analysis = self._analyze_project_with_shotgun(shotgun_context)

            # Create enhanced configuration
            config = {
                "project_path": project_path,
                "user_request": user_request,
                "shotgun_context": shotgun_context,
                "project_analysis": project_analysis,
                "enhanced_features": {
                    "complete_codebase_context": True,
                    "smart_file_filtering": True,
                    "structured_output": True,
                    "template_oriented_planning": True
                },
                "integration_metadata": {
                    "bridge_version": "1.0.0",
                    "shotgun_available": self.shotgun_path is not None,
                    "workflow_available": self.workflow_path is not None
                }
            }

            return config

        except Exception as e:
            logger.error(f"Error creating enhanced config: {e}")
            return self._create_fallback_config(project_path, user_request)

    def _generate_shotgun_context(self, project_path: str) -> Dict[str, Any]:
        """Generate context using shotgun-code."""
        if not self.shotgun_path:
            logger.warning("Shotgun not available, using fallback")
            return self._generate_fallback_context(project_path)

        try:
            # Build shotgun if needed
            self._ensure_shotgun_built()

            # Run shotgun to generate context
            executable = self._get_shotgun_executable()
            if not executable:
                return self._generate_fallback_context(project_path)

            # Create temporary output file
            output_file = os.path.join(self.temp_dir, "shotgun_output.txt")

            # Run shotgun command
            cmd = [
                executable,
                "--path", project_path,
                "--output", output_file,
                "--exclude", "node_modules",
                "--exclude", ".git",
                "--exclude", "__pycache__",
                "--exclude", "*.pyc",
                "--exclude", "build",
                "--exclude", "dist"
            ]

            logger.info(f"Running shotgun: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

            if result.returncode == 0 and os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    raw_output = f.read()

                return self._parse_shotgun_output(raw_output)
            else:
                logger.error(f"Shotgun execution failed: {result.stderr}")
                return self._generate_fallback_context(project_path)

        except Exception as e:
            logger.error(f"Error generating shotgun context: {e}")
            return self._generate_fallback_context(project_path)

    def _ensure_shotgun_built(self):
        """Ensure shotgun is built and ready."""
        if not self.shotgun_path:
            return

        executable = self._get_shotgun_executable()
        if executable and os.path.exists(executable):
            return  # Already built

        try:
            # Build shotgun
            logger.info("Building shotgun-code...")
            result = subprocess.run(
                ["wails", "build"],
                cwd=self.shotgun_path,
                capture_output=True,
                text=True,
                timeout=300
            )

            if result.returncode == 0:
                logger.info("Shotgun built successfully")
            else:
                logger.error(f"Failed to build shotgun: {result.stderr}")

        except Exception as e:
            logger.error(f"Error building shotgun: {e}")

    def _get_shotgun_executable(self) -> Optional[str]:
        """Get path to shotgun executable."""
        if not self.shotgun_path:
            return None

        possible_names = ["shotgun-code", "shotgun-code.exe", "shotgun_code", "shotgun_code.exe"]
        build_dir = os.path.join(self.shotgun_path, "build", "bin")

        for name in possible_names:
            exe_path = os.path.join(build_dir, name)
            if os.path.exists(exe_path) and os.access(exe_path, os.X_OK):
                return exe_path

        return None

    def _parse_shotgun_output(self, raw_output: str) -> Dict[str, Any]:
        """Parse shotgun output into structured format."""
        context = {
            "generation_method": "shotgun-code",
            "tree_structure": "",
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {}
            }
        }

        try:
            lines = raw_output.split('\n')
            current_file = None
            current_content = []
            in_file_content = False

            for line in lines:
                # Check for file start marker
                if line.startswith('<file path="') and line.endswith('">'):
                    # Save previous file
                    if current_file and current_content:
                        context["files"][current_file] = '\n'.join(current_content)

                    # Start new file
                    current_file = line.split('"')[1]
                    current_content = []
                    in_file_content = True

                elif line == '</file>':
                    # End of file
                    if current_file and current_content:
                        context["files"][current_file] = '\n'.join(current_content)
                    current_file = None
                    current_content = []
                    in_file_content = False

                elif in_file_content and current_file:
                    # File content
                    current_content.append(line)

                elif not in_file_content:
                    # Tree structure or other content
                    if context["tree_structure"]:
                        context["tree_structure"] += '\n' + line
                    else:
                        context["tree_structure"] = line

            # Handle last file
            if current_file and current_content:
                context["files"][current_file] = '\n'.join(current_content)

            # Calculate statistics
            context["statistics"]["total_files"] = len(context["files"])
            for content in context["files"].values():
                context["statistics"]["total_size"] += len(content)

            logger.info(f"Parsed {context['statistics']['total_files']} files from shotgun output")
            return context

        except Exception as e:
            logger.error(f"Error parsing shotgun output: {e}")
            return context

    def _generate_fallback_context(self, project_path: str) -> Dict[str, Any]:
        """Generate fallback context when shotgun is not available."""
        logger.info("Generating fallback context")

        context = {
            "generation_method": "fallback",
            "tree_structure": "",
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {}
            }
        }

        try:
            project_path = Path(project_path)
            if not project_path.exists():
                return context

            # Simple file collection
            include_extensions = {'.py', '.js', '.ts', '.html', '.css', '.json', '.md', '.yml', '.yaml'}
            exclude_dirs = {'node_modules', '.git', '__pycache__', '.venv', 'venv', 'build', 'dist'}

            for file_path in project_path.rglob('*'):
                if file_path.is_file():
                    # Skip excluded directories
                    if any(excluded in file_path.parts for excluded in exclude_dirs):
                        continue

                    # Check extension
                    if file_path.suffix.lower() in include_extensions:
                        try:
                            relative_path = file_path.relative_to(project_path)
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()
                            context["files"][str(relative_path)] = content
                        except (UnicodeDecodeError, PermissionError):
                            continue

            # Generate tree
            tree_lines = []
            for file_path in sorted(context["files"].keys()):
                tree_lines.append(f"├── {file_path}")
            context["tree_structure"] = '\n'.join(tree_lines)

            # Statistics
            context["statistics"]["total_files"] = len(context["files"])
            for content in context["files"].values():
                context["statistics"]["total_size"] += len(content)

            return context

        except Exception as e:
            logger.error(f"Error in fallback context generation: {e}")
            return context

    def _analyze_project_with_shotgun(self, shotgun_context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project structure using shotgun context."""
        analysis = {
            "primary_languages": [],
            "frameworks": [],
            "build_tools": [],
            "key_components": [],
            "project_type": "unknown",
            "shotgun_enhanced": True
        }

        files = shotgun_context.get("files", {})

        # Language detection
        extensions = {}
        for file_path in files.keys():
            if '.' in file_path:
                ext = Path(file_path).suffix.lower()
                extensions[ext] = extensions.get(ext, 0) + 1

        # Map to languages
        ext_to_lang = {
            '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
            '.html': 'HTML', '.css': 'CSS', '.go': 'Go', '.rs': 'Rust',
            '.java': 'Java', '.cpp': 'C++', '.c': 'C'
        }

        for ext, count in sorted(extensions.items(), key=lambda x: x[1], reverse=True)[:3]:
            if ext in ext_to_lang:
                analysis["primary_languages"].append(ext_to_lang[ext])

        # Framework detection
        framework_files = {
            'package.json': 'Node.js',
            'requirements.txt': 'Python',
            'go.mod': 'Go',
            'Cargo.toml': 'Rust',
            'pom.xml': 'Maven',
            'build.gradle': 'Gradle'
        }

        for file_path in files.keys():
            filename = Path(file_path).name
            if filename in framework_files:
                analysis["frameworks"].append(framework_files[filename])

        return analysis

    def _create_fallback_config(self, project_path: str, user_request: str) -> Dict[str, Any]:
        """Create fallback configuration."""
        return {
            "project_path": project_path,
            "user_request": user_request,
            "shotgun_context": self._generate_fallback_context(project_path),
            "project_analysis": {"primary_languages": [], "frameworks": [], "shotgun_enhanced": False},
            "enhanced_features": {"complete_codebase_context": False},
            "integration_metadata": {"bridge_version": "1.0.0", "shotgun_available": False}
        }

    def cleanup(self):
        """Clean up temporary files."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info("Cleaned up temporary files")
        except Exception as e:
            logger.error(f"Error cleaning up: {e}")


# Example usage
if __name__ == "__main__":
    bridge = ShotgunBridge()

    config = bridge.create_enhanced_workflow_config(
        project_path=".",
        user_request="Add dark mode toggle to the UI"
    )

    print(f"Enhanced config created:")
    print(f"- Files analyzed: {config['shotgun_context']['statistics']['total_files']}")
    print(f"- Shotgun available: {config['integration_metadata']['shotgun_available']}")
    print(f"- Enhanced features: {list(config['enhanced_features'].keys())}")

    bridge.cleanup()
