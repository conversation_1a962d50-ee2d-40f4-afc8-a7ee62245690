#!/usr/bin/env python3
"""
Gitea Connection Test Script
Tests Gitea connectivity and helps troubleshoot authentication issues.
"""

import requests
import json
import sys
import os

def test_gitea_connection():
    """Test Gitea connection with current configuration."""
    
    # Load configuration
    gitea_url = os.environ.get("GITEA_URL", "http://*************:3000")
    gitea_token = os.environ.get("GITEA_ACCESS_TOKEN", "")
    gitea_username = os.environ.get("GITEA_USERNAME", "Admin")
    
    print("🔍 Gitea Connection Test")
    print("=" * 50)
    print(f"🔗 URL: {gitea_url}")
    print(f"👤 Username: {gitea_username}")
    print(f"🔑 Token: {'*' * (len(gitea_token) - 4) + gitea_token[-4:] if len(gitea_token) > 4 else 'NOT_SET'}")
    print()
    
    # Test 1: Basic connectivity
    print("📡 Test 1: Basic Connectivity")
    try:
        response = requests.get(f"{gitea_url}/api/v1/version", timeout=10)
        if response.status_code == 200:
            version_info = response.json()
            print(f"✅ Gitea server is accessible")
            print(f"   Version: {version_info.get('version', 'Unknown')}")
        else:
            print(f"⚠️  Server responded with status {response.status_code}")
            print(f"   Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Cannot connect to Gitea server: {e}")
        return False
    
    print()
    
    # Test 2: Authentication
    print("🔐 Test 2: Authentication")
    if not gitea_token:
        print("❌ No access token configured")
        print_token_instructions(gitea_url)
        return False
    
    headers = {"Authorization": f"token {gitea_token}"}
    try:
        response = requests.get(f"{gitea_url}/api/v1/user", headers=headers, timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ Authentication successful!")
            print(f"   User: {user_info.get('login', 'Unknown')}")
            print(f"   Full Name: {user_info.get('full_name', 'Unknown')}")
            print(f"   Email: {user_info.get('email', 'Unknown')}")
            return True
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"   Response: {response.text}")
            print_token_instructions(gitea_url)
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Error during authentication test: {e}")
        return False

def print_token_instructions(gitea_url):
    """Print instructions for creating a new token."""
    print()
    print("🛠️  How to Create a Valid Access Token:")
    print("=" * 50)
    print(f"1. Open your browser and go to: {gitea_url}")
    print("2. Log in with your credentials")
    print("3. Go to Settings → Applications → Manage Access Tokens")
    print("4. Click 'Generate New Token'")
    print("5. Token Name: 'Workflow-System-Token'")
    print("6. Select these scopes:")
    print("   ✅ repo")
    print("   ✅ read:user")
    print("   ✅ read:org")
    print("   ✅ read:repository")
    print("7. Click 'Generate Token'")
    print("8. Copy the token immediately (you won't see it again)")
    print("9. Update config/local_config.sh with the new token")
    print()

def interactive_token_test():
    """Interactive token testing."""
    print("🔧 Interactive Token Test")
    print("=" * 30)
    
    url = input(f"Gitea URL [http://*************:3000]: ").strip() or "http://*************:3000"
    username = input(f"Username [Admin]: ").strip() or "Admin"
    token = input("Access Token: ").strip()
    
    if not token:
        print("❌ Token is required for testing")
        return
    
    # Test the provided credentials
    headers = {"Authorization": f"token {token}"}
    try:
        response = requests.get(f"{url}/api/v1/user", headers=headers, timeout=10)
        if response.status_code == 200:
            user_info = response.json()
            print(f"✅ Token is valid!")
            print(f"   User: {user_info.get('login', 'Unknown')}")
            print()
            print("🔧 To use this token, update config/local_config.sh:")
            print(f'export GITEA_URL="{url}"')
            print(f'export GITEA_ACCESS_TOKEN="{token}"')
            print(f'export GITEA_USERNAME="{username}"')
        else:
            print(f"❌ Token is invalid: {response.status_code}")
            print(f"   Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Error testing token: {e}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        interactive_token_test()
    else:
        success = test_gitea_connection()
        if not success:
            print()
            print("💡 Run with --interactive to test a new token:")
            print("   python test_gitea_connection.py --interactive")
        sys.exit(0 if success else 1)
