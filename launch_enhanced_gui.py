#!/usr/bin/env python3
"""
Enhanced Workflow GUI Launcher
Launches the enhanced workflow system with multi-repository context capabilities.
"""

import os
import sys
import logging
from pathlib import Path

def setup_environment():
    """Setup environment and paths."""
    # Add integration directory to Python path
    integration_path = Path(__file__).parent / "integration"
    if str(integration_path) not in sys.path:
        sys.path.insert(0, str(integration_path))
    
    # Add workflow directory to Python path
    workflow_path = Path(__file__).parent / "workflow"
    if str(workflow_path) not in sys.path:
        sys.path.insert(0, str(workflow_path))
    
    # Load configuration if available
    config_file = Path(__file__).parent / "config" / "local_config.sh"
    if config_file.exists():
        print(f"✅ Configuration file found: {config_file}")
    else:
        print(f"⚠️  Configuration file not found: {config_file}")
        print("   Some features may not work without proper configuration.")

def main():
    """Main launcher function."""
    print("🚀 Enhanced Workflow System Launcher")
    print("=" * 50)
    
    # Setup environment
    setup_environment()
    
    try:
        # Test if we can launch the regular GUI first
        print("🔍 Testing basic GUI components...")
        
        # Import and test basic components
        from unified_workflow_gui import UnifiedWorkflowGUI
        print("✅ Basic GUI components available")
        
        # Try to import enhanced components
        try:
            from enhanced_workflow_gui import EnhancedWorkflowGUI
            print("✅ Enhanced GUI components available")
            use_enhanced = True
        except ImportError as e:
            print(f"⚠️  Enhanced GUI not available: {e}")
            print("   Falling back to basic GUI")
            use_enhanced = False
        
        # Launch appropriate GUI
        if use_enhanced:
            print("🚀 Launching Enhanced Workflow GUI...")
            app = EnhancedWorkflowGUI()
        else:
            print("🚀 Launching Basic Workflow GUI...")
            app = UnifiedWorkflowGUI()
        
        print("✅ GUI launched successfully!")
        print("\n💡 Features available:")
        print("   • Gitea repository integration")
        print("   • Local project analysis")
        print("   • Enhanced context generation")
        print("   • AI-powered implementation planning")
        
        if use_enhanced:
            print("   • Multi-repository context generation")
            print("   • Cross-project code analysis")
            print("   • Comprehensive codebase dumps")
        
        print("\n🖥️  Starting application...")
        app.root.mainloop()
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("\n🔧 Troubleshooting:")
        print("   1. Make sure you're in the correct directory")
        print("   2. Check that all required files are present")
        print("   3. Verify Python environment is set up correctly")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Error launching GUI: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
