#!/usr/bin/env python3
"""
Shotgun-Code Integration for Workflow System
Provides enhanced context generation using shotgun-code's codebase blasting capabilities.
"""

import os
import sys
import json
import subprocess
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List
import xml.etree.ElementTree as ET

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("ShotgunConnector")

class ShotgunConnector:
    """
    Integrates shotgun-code for enhanced codebase context generation.
    Provides both CLI and programmatic interfaces to shotgun functionality.
    """

    def __init__(self, shotgun_path: Optional[str] = None):
        """
        Initialize the shotgun connector.

        Args:
            shotgun_path: Path to shotgun-code executable. If None, will search for it.
        """
        self.shotgun_path = shotgun_path or self._find_shotgun_executable()
        self.temp_dir = tempfile.mkdtemp(prefix="workflow_shotgun_")
        logger.info(f"ShotgunConnector initialized with temp dir: {self.temp_dir}")

        if not self.shotgun_path:
            logger.warning("Shotgun executable not found. Some features will be disabled.")
        else:
            logger.info(f"Using shotgun executable: {self.shotgun_path}")

    def _find_shotgun_executable(self) -> Optional[str]:
        """Find shotgun-code executable in common locations."""
        # Shotgun-code directory has been removed - using fallback mode only
        logger.info("Shotgun-code executable not available - using enhanced fallback mode")
        return None

    def generate_codebase_context(self,
                                repository_path: str,
                                exclude_patterns: Optional[List[str]] = None,
                                include_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Generate comprehensive codebase context.

        Note: Shotgun-code is a GUI-only application and cannot be used programmatically.
        This method will use the fallback context generation which provides similar functionality.

        Args:
            repository_path: Path to the repository to analyze
            exclude_patterns: List of patterns to exclude (e.g., ["node_modules", "*.log"])
            include_patterns: List of patterns to include (e.g., ["*.py", "*.js"])

        Returns:
            Dictionary containing structured codebase context
        """
        logger.info("Shotgun-code is GUI-only. Using enhanced fallback context generation.")

        # Use enhanced fallback that mimics shotgun-code's output format
        return self._enhanced_fallback_context_generation(
            repository_path, exclude_patterns, include_patterns
        )

    def _parse_shotgun_output(self, shotgun_output: str, repository_path: str) -> Dict[str, Any]:
        """
        Parse shotgun-code output into structured format.

        Args:
            shotgun_output: Raw output from shotgun-code
            repository_path: Original repository path

        Returns:
            Structured context dictionary
        """
        context = {
            "repository_path": repository_path,
            "generation_method": "shotgun-code",
            "tree_structure": "",
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {}
            }
        }

        try:
            # Split output into tree and file sections
            lines = shotgun_output.split('\n')

            # Find tree section (usually at the beginning)
            tree_lines = []
            file_content_started = False
            current_file = None
            current_content = []

            for line in lines:
                # Check for file delimiters (shotgun uses specific patterns)
                if line.startswith('<file path="') and line.endswith('">'):
                    # Start of file content
                    if current_file and current_content:
                        context["files"][current_file] = '\n'.join(current_content)

                    # Extract file path
                    current_file = line.split('"')[1]
                    current_content = []
                    file_content_started = True

                elif line == '</file>':
                    # End of file content
                    if current_file and current_content:
                        context["files"][current_file] = '\n'.join(current_content)
                    current_file = None
                    current_content = []

                elif file_content_started and current_file:
                    # File content line
                    current_content.append(line)

                elif not file_content_started:
                    # Tree structure line
                    tree_lines.append(line)

            # Handle last file if exists
            if current_file and current_content:
                context["files"][current_file] = '\n'.join(current_content)

            # Set tree structure
            context["tree_structure"] = '\n'.join(tree_lines)

            # Calculate statistics
            context["statistics"]["total_files"] = len(context["files"])

            for file_path, content in context["files"].items():
                context["statistics"]["total_size"] += len(content)

                # Count file types
                ext = Path(file_path).suffix.lower()
                if ext:
                    context["statistics"]["file_types"][ext] = \
                        context["statistics"]["file_types"].get(ext, 0) + 1

            logger.info(f"Parsed {context['statistics']['total_files']} files from shotgun output")
            return context

        except Exception as e:
            logger.error(f"Error parsing shotgun output: {e}")
            return self._fallback_context_generation(repository_path)

    def _enhanced_fallback_context_generation(self,
                                            repository_path: str,
                                            exclude_patterns: Optional[List[str]] = None,
                                            include_patterns: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Enhanced fallback context generation that mimics shotgun-code's functionality.

        Args:
            repository_path: Path to repository
            exclude_patterns: Patterns to exclude
            include_patterns: Patterns to include

        Returns:
            Enhanced context dictionary with shotgun-like structure
        """
        logger.info("Using enhanced fallback context generation (shotgun-like)")

        context = {
            "repository_path": repository_path,
            "generation_method": "enhanced-fallback",
            "tree_structure": "",
            "files": {},
            "statistics": {
                "total_files": 0,
                "total_size": 0,
                "file_types": {}
            }
        }

        try:
            repo_path = Path(repository_path)
            if not repo_path.exists():
                logger.error(f"Repository path does not exist: {repository_path}")
                return context

            # Enhanced exclude patterns (combining defaults with user patterns)
            default_exclude_patterns = {
                'node_modules', '.git', '__pycache__', '.venv', 'venv', 'build', 'dist',
                '.next', '.nuxt', 'coverage', '.nyc_output', '.pytest_cache',
                'target', 'vendor', '.gradle', '.mvn'
            }

            if exclude_patterns:
                for pattern in exclude_patterns:
                    default_exclude_patterns.add(pattern.replace('*', '').replace('.', ''))

            # Enhanced include patterns
            default_include_extensions = {
                '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss', '.sass',
                '.json', '.yaml', '.yml', '.md', '.txt', '.sh', '.bat', '.go', '.rs',
                '.java', '.c', '.cpp', '.h', '.hpp', '.php', '.rb', '.sql', '.toml',
                '.ini', '.conf', '.env'
            }

            if include_patterns:
                # Convert patterns like "*.py" to ".py"
                for pattern in include_patterns:
                    if pattern.startswith('*'):
                        default_include_extensions.add(pattern[1:])
                    elif '.' in pattern:
                        default_include_extensions.add('.' + pattern.split('.')[-1])

            # Build tree structure and collect files
            tree_lines = []
            tree_lines.append(f"{repo_path.name}/")

            def build_tree_recursive(current_path: Path, prefix: str = "", is_last: bool = True):
                """Recursively build tree structure like shotgun-code."""
                try:
                    entries = sorted(current_path.iterdir(), key=lambda x: (not x.is_dir(), x.name.lower()))
                    visible_entries = []

                    for entry in entries:
                        # Skip excluded directories/files
                        if any(excluded in entry.parts for excluded in default_exclude_patterns):
                            continue
                        if entry.name.startswith('.') and entry.name not in {'.env', '.gitignore', '.dockerignore'}:
                            continue
                        visible_entries.append(entry)

                    for i, entry in enumerate(visible_entries):
                        is_entry_last = i == len(visible_entries) - 1
                        branch = "└── " if is_entry_last else "├── "
                        tree_lines.append(f"{prefix}{branch}{entry.name}")

                        if entry.is_dir():
                            next_prefix = prefix + ("    " if is_entry_last else "│   ")
                            build_tree_recursive(entry, next_prefix, is_entry_last)
                        elif entry.suffix.lower() in default_include_extensions:
                            # Add file to context
                            try:
                                relative_path = entry.relative_to(repo_path)
                                with open(entry, 'r', encoding='utf-8') as f:
                                    content = f.read()
                                context["files"][str(relative_path)] = content
                            except (UnicodeDecodeError, PermissionError, OSError):
                                # Skip binary or inaccessible files
                                continue

                except (PermissionError, OSError) as e:
                    logger.warning(f"Cannot access directory {current_path}: {e}")

            # Build the tree
            build_tree_recursive(repo_path)
            context["tree_structure"] = '\n'.join(tree_lines)

            # Calculate statistics
            context["statistics"]["total_files"] = len(context["files"])
            for file_path, content in context["files"].items():
                context["statistics"]["total_size"] += len(content)

                # Count file types
                ext = Path(file_path).suffix.lower()
                if ext:
                    context["statistics"]["file_types"][ext] = \
                        context["statistics"]["file_types"].get(ext, 0) + 1

            logger.info(f"Enhanced fallback generated context for {context['statistics']['total_files']} files")
            logger.info(f"File types found: {list(context['statistics']['file_types'].keys())}")
            return context

        except Exception as e:
            logger.error(f"Error in enhanced fallback context generation: {e}")
            return context

    def _fallback_context_generation(self, repository_path: str) -> Dict[str, Any]:
        """
        Basic fallback context generation.

        Args:
            repository_path: Path to repository

        Returns:
            Basic context dictionary
        """
        return self._enhanced_fallback_context_generation(repository_path)

    def cleanup(self):
        """Clean up temporary files."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info("Cleaned up temporary files")
        except Exception as e:
            logger.error(f"Error cleaning up: {e}")


# Example usage
if __name__ == "__main__":
    connector = ShotgunConnector()

    # Test with current directory
    context = connector.generate_codebase_context(
        repository_path=".",
        exclude_patterns=["node_modules", "*.log", "__pycache__"],
        include_patterns=["*.py", "*.js", "*.md"]
    )

    print(f"Generated context with {context['statistics']['total_files']} files")
    print(f"Total size: {context['statistics']['total_size']} characters")
    print(f"File types: {context['statistics']['file_types']}")

    connector.cleanup()
