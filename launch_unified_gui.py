#!/usr/bin/env python3
"""
Unified Workflow GUI Launcher
Launches the unified workflow system with Gitea integration and shotgun-enhanced context generation.
"""

import os
import sys
import tkinter as tk
from tkinter import messagebox
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []

    # Check tkinter
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")

    # Check requests for Gitea API
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    # Check if workflow directory exists
    if not os.path.exists("workflow"):
        missing_deps.append("workflow directory")

    # Check if integration directory exists
    if not os.path.exists("integration"):
        missing_deps.append("integration directory")

    return missing_deps

def setup_environment():
    """Setup the Python path and environment."""
    # Add current directory to Python path
    if "." not in sys.path:
        sys.path.insert(0, ".")

    # Add workflow directory to Python path
    workflow_path = os.path.join(os.getcwd(), "workflow")
    if workflow_path not in sys.path:
        sys.path.insert(0, workflow_path)

    # Add integration directory to Python path
    integration_path = os.path.join(os.getcwd(), "integration")
    if integration_path not in sys.path:
        sys.path.insert(0, integration_path)

def show_error_dialog(title, message):
    """Show error dialog using tkinter."""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    messagebox.showerror(title, message)
    root.destroy()

def show_welcome_message():
    """Show welcome message."""
    welcome_text = """
🚀 Unified Workflow System

This application combines:
• Gitea repository integration
• Shotgun-enhanced context generation
• AI-powered implementation planning
• Unified desktop interface

Features:
✅ Connect to Gitea and browse repositories
✅ Generate comprehensive project context
✅ Create detailed implementation plans
✅ Export and save results

Click OK to launch the application!
    """

    root = tk.Tk()
    root.withdraw()
    result = messagebox.showinfo("Welcome to Unified Workflow System", welcome_text)
    root.destroy()
    return result

def main():
    """Main entry point for unified GUI launcher."""
    print("🚀 Launching Unified Workflow System...")

    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        error_msg = f"Missing dependencies: {', '.join(missing_deps)}\n\n"
        error_msg += "Please ensure:\n"
        error_msg += "1. You're running from the workflow project root directory\n"
        error_msg += "2. The shotgun integration is properly installed\n"
        error_msg += "3. Install missing packages: pip install requests\n"
        error_msg += "4. Run: ./install_shotgun_integration.sh"

        print(f"❌ Error: {error_msg}")
        try:
            show_error_dialog("Missing Dependencies", error_msg)
        except:
            pass
        return 1

    # Setup environment
    setup_environment()

    # Skip welcome message for now to avoid GUI blocking
    # try:
    #     show_welcome_message()
    # except:
    #     pass  # Continue even if welcome dialog fails

    try:
        # Import and launch unified GUI
        print("📦 Importing unified GUI components...")
        from unified_workflow_gui import UnifiedWorkflowGUI

        print("🖥️  Starting unified workflow application...")
        app = UnifiedWorkflowGUI()

        # Show startup message
        print("✅ Unified GUI launched successfully!")
        print("💡 Features available:")
        print("   • Gitea repository integration")
        print("   • Local project analysis")
        print("   • Shotgun-enhanced context generation")
        print("   • AI-powered implementation planning")
        print("   • Export and save functionality")

        # Run the application
        app.run()

        print("👋 Unified workflow application closed.")
        return 0

    except ImportError as e:
        error_msg = f"Failed to import unified GUI components: {e}\n\n"
        error_msg += "This usually means:\n"
        error_msg += "1. The shotgun integration is not properly installed\n"
        error_msg += "2. Missing Python dependencies\n"
        error_msg += "3. Environment variables not set\n\n"
        error_msg += "Try running: ./install_shotgun_integration.sh"

        print(f"❌ Import Error: {error_msg}")
        try:
            show_error_dialog("Import Error", error_msg)
        except:
            pass
        return 1

    except Exception as e:
        error_msg = f"Unexpected error: {e}\n\n"
        error_msg += "Please check the console output for more details."

        print(f"❌ Unexpected Error: {error_msg}")
        logger.exception("Unexpected error in unified GUI launcher")
        try:
            show_error_dialog("Unexpected Error", error_msg)
        except:
            pass
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
