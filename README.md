# 🚀 Workflow - AI-Powered Development Workflow System

An intelligent workflow system that generates structured implementation plans from natural language requests using AI and repository context analysis.

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## ✨ Features

- 🤖 **AI-Powered Planning**: Converts natural language requests into detailed implementation plans
- 📋 **Structured Output**: Generates JSON plans with phases, file targets, and validation steps
- 🔗 **Repository Integration**: Automatically fetches and analyzes repository context via Gitea
- 🔄 **Multi-Repository Context**: Combine context from multiple local projects for comprehensive code analysis
- 🎯 **Pattern Extraction**: Automatically identify and extract code patterns across projects
- 📊 **AI-Ready Exports**: Generate comprehensive context dumps (<PERSON>down, JSON, Text) for AI model analysis
- 🌐 **Cross-Platform**: Works on Windows, Linux, and macOS
- ⚡ **Easy Setup**: Simple configuration and one-command execution
- 🚀 **MCP Integration**: Uses Model Context Protocol for efficient repository access
- 💾 **Smart Caching**: File-based caching for improved performance
- 🖥️ **Enhanced GUI**: Unified interface with multi-repository context management

## 🏗️ Architecture

```
User Input → Gitea-MCP → Context Builder → AI Model → Implementation Plan
```

### Core Components

- **workflow/main.py**: Main orchestrator and entry point
- **workflow/trigger.py**: Input parsing and validation
- **workflow/gitea_connector.py**: Gitea repository context fetching via MCP
- **workflow/context_builder.py**: Context combination and analysis
- **workflow/model_interface.py**: AI model interface and plan generation
- **mcp/server.py**: Gitea MCP server implementation
- **mcp/client.py**: Simplified MCP client for repository operations

### 🔄 Multi-Repository Context Workflow

The system now supports advanced multi-repository context generation for comprehensive code analysis:

```mermaid
graph TD
    A[Local Projects] --> D[Multi-Repo Context Manager]
    B[Gitea Repositories] --> D
    C[Target Repository] --> D

    D --> E[Combined Context Generation]
    E --> F[Code Examples by Language]
    E --> G[Pattern Extraction]
    E --> H[File Categorization]

    F --> I[AI-Ready Export]
    G --> I
    H --> I

    I --> J[Markdown Report]
    I --> K[JSON Data]
    I --> L[Text Dump for AI]

    L --> M[AI Model Analysis]
    M --> N[Enhanced Code Generation]
    N --> O[Target Repository Improvements]

    style C fill:#ff9999
    style D fill:#99ccff
    style I fill:#99ff99
    style M fill:#ffcc99
```

**Key Features:**
- **Multi-Project Analysis**: Combine context from multiple local projects and repositories
- **Intelligent Code Organization**: Categorize files by type (API, database, frontend, utilities)
- **Pattern Extraction**: Identify and extract code patterns across projects
- **AI-Ready Exports**: Generate comprehensive context dumps for AI model analysis
- **Priority System**: Target repository gets highest priority, with supporting context from other projects

## 🚀 Quick Start

### 1. Setup

```bash
# Clone and navigate to the project
cd workflow

# Install dependencies and create secure config templates
python setup.py

# Configure your credentials (IMPORTANT: Use local config files)
# Linux/Mac:
cp config/config.sh config/local_config.sh
nano config/local_config.sh  # Edit with your actual credentials
source config/local_config.sh

# Windows:
copy config\config.bat config\local_config.bat
notepad config\local_config.bat  # Edit with your actual credentials
config\local_config.bat
```

**🔒 Security Note:** Never commit `local_config.*` files to version control. They contain your sensitive credentials and are automatically excluded by `.gitignore`.

### 2. Usage

```bash
cd workflow
python main.py
```

The system will guide you through an interactive workflow:

1. **Select Project**: Choose from available projects or enter custom
2. **Select Branch**: Choose from common branches or enter custom
3. **Enter Query**: Describe what you want to implement
4. **Confirm & Execute**: Review and run the workflow

### 3. Example Session

```
🔍 Select a project:
  1. Forge/maestro
  2. Test/Oracle
  3. Enter custom project...

Select option (1-3): 1
✅ Selected project: Forge/maestro

🔍 Select a branch:
  1. main
  2. master
  3. develop
  4. Enter custom branch...

Select option (1-4): 2
✅ Selected branch: master

🤖 Your request: Add dark mode toggle to the UI

🚀 Proceed with workflow? (y/N): y
```

### 4. Multi-Repository Context Generation

For comprehensive code analysis across multiple projects:

```bash
# Launch enhanced GUI with multi-repo capabilities
python launch_enhanced_gui.py

# Or use command line demo
python demo_multi_repo_context.py --mode basic

# Targeted analysis for specific repository
python demo_multi_repo_context.py --mode targeted \
  --target /path/to/target/repo \
  --projects /path/to/project1 /path/to/project2
```

**Multi-Repository Features:**
- **Auto-Discovery**: Automatically find projects in directories
- **Priority System**: Set target repository with highest priority
- **Code Categorization**: Organize by file type (API, database, frontend, etc.)
- **Pattern Extraction**: Extract function/class definitions and patterns
- **Export Formats**: Markdown (review), JSON (programmatic), Text (AI input)

## 📋 Configuration

**🔒 IMPORTANT:** Always use local configuration files for your credentials:

### Secure Configuration Method

1. **Create local config files:**
   ```bash
   # Linux/Mac
   cp config/config.sh config/local_config.sh

   # Windows
   copy config\config.bat config\local_config.bat
   ```

2. **Edit with your actual credentials:**
   - **GITEA_URL**: Your Gitea instance URL (e.g., `https://gitea.example.com`)
   - **GITEA_ACCESS_TOKEN**: Personal access token from Gitea Settings > Applications
   - **GITMCP_SERVER_URL**: Gitea-MCP server endpoint
   - **OPENAI_API_BASE_URL**: OpenAI-compatible API endpoint

3. **Alternative: Use .env file:**
   ```bash
   cp config/env.example .env
   nano .env  # Edit with your credentials
   ```

**⚠️ Security Warning:**
- Never commit `local_config.*` or `.env` files
- Never edit the template files (`config.sh`, `config.bat`) with real credentials
- Use HTTPS URLs when possible
- Generate tokens with minimal required permissions

## 📁 Output

The system generates:
- **plan.json**: Structured implementation plan
- **Phase-based breakdown**: Clear steps with file targets and validation
- **Context-aware suggestions**: Based on actual repository analysis

## 📋 Dependencies

### System Requirements

- **Python 3.8+** (3.11+ recommended)
- **Git** (recommended for repository operations)
- **Access to Gitea instance** (for repository context)
- **OpenAI-compatible LLM server** (for plan generation)

### Python Dependencies

#### Core Dependencies
```
requests>=2.26.0          # HTTP requests for API interactions
python-dotenv>=0.19.0     # Environment variable management
pydantic>=1.8.0           # JSON handling and validation
GitPython>=3.1.0          # Git operations (optional)
```

#### AI/LLM Integration
```
langchain>=0.0.267        # LangChain framework
langchain-openai>=0.0.1   # OpenAI integration
langchain-core>=0.0.1     # LangChain core components
langchain-community       # Community integrations (VLLMOpenAI)
```

#### MCP (Model Context Protocol)
```
mcp>=0.0.1                    # Base MCP package
langchain-mcp-adapters>=0.0.1 # LangChain MCP integration
python-multipart>=0.0.6       # Multipart request support
```

#### Server Components
```
aiohttp>=3.8.0            # Async HTTP server (for MCP server)
asyncio                   # Async programming support
```

### Optional Dependencies

#### Development Tools
```
pytest>=6.0.0             # Testing framework
pytest-cov>=2.0.0         # Coverage reporting
black>=22.0.0             # Code formatting
flake8>=4.0.0             # Code linting
mypy>=0.900               # Type checking
```

#### Performance & Monitoring
```
memory-profiler           # Memory usage profiling
psutil                    # System monitoring
```

### Installation

#### Quick Install
```bash
# Install all dependencies
pip install -r requirements.txt

# Or use the setup script
python setup.py
```

#### Manual Installation
```bash
# Core dependencies
pip install requests python-dotenv pydantic GitPython

# AI/LLM components
pip install langchain langchain-openai langchain-core langchain-community

# MCP components
pip install mcp langchain-mcp-adapters python-multipart

# Server components
pip install aiohttp
```

#### Development Setup
```bash
# Install development dependencies
pip install pytest pytest-cov black flake8 mypy memory-profiler

# Or install from dev requirements
pip install -r requirements-dev.txt  # If available
```

### External Services

#### Required Services
- **Gitea Server**: Repository hosting and API access
- **OpenAI-compatible LLM**: For plan generation (e.g., OpenAI API, local models via vLLM, Ollama)

#### Optional Services
- **Redis/Memcached**: For advanced caching (future enhancement)
- **PostgreSQL/MySQL**: For persistent storage (future enhancement)

## 📁 Project Structure

```
workflow/
├── workflow/               # Core workflow components
│   ├── __init__.py
│   ├── main.py            # Main entry point
│   ├── enhanced_main.py   # Enhanced workflow with shotgun integration
│   ├── trigger.py         # User input handling
│   ├── gitea_connector.py # Repository context fetching
│   ├── context_builder.py # Context combination
│   ├── enhanced_context_builder.py # Enhanced context with multi-repo
│   ├── model_interface.py # AI model interface
│   └── shotgun_connector.py # Shotgun-style context generation
├── integration/           # Integration components
│   ├── __init__.py
│   ├── unified_workflow_gui.py # Main GUI interface
│   ├── enhanced_workflow_gui.py # Enhanced GUI with multi-repo
│   ├── multi_repo_context_manager.py # Multi-repository context system
│   ├── gitea_repo_manager.py # Gitea repository operations
│   ├── shotgun_bridge.py  # Shotgun integration bridge
│   └── gui_launcher.py    # GUI launcher utilities
├── mcp/                   # MCP server and client
│   ├── __init__.py
│   ├── server.py         # Gitea MCP server
│   └── client.py         # Simplified MCP client
├── utils/                 # Utility modules
│   ├── __init__.py
│   ├── banner.py         # UI utilities
│   ├── context_optimizer.py
│   └── file_cache.py     # Caching system
├── config/               # Configuration files
│   ├── config.sh         # Linux/Mac config template
│   ├── config.bat        # Windows config template
│   ├── local_config.sh   # Local config (not in git)
│   └── env.example       # Environment variables example
├── docs/                 # Documentation
│   ├── ARCHITECTURE.md   # System architecture
│   ├── DEVELOPMENT.md    # Development guide
│   ├── SECURITY.md       # Security guidelines
│   ├── GITEA_TROUBLESHOOTING.md # Gitea setup guide
│   └── UNIFIED_WORKFLOW_GUIDE.md # GUI usage guide
├── examples/             # Example files
├── tests/                # Test suite
├── .github/              # GitHub workflows
├── launch_unified_gui.py # Main GUI launcher
├── launch_enhanced_gui.py # Enhanced GUI launcher
├── demo_multi_repo_context.py # Multi-repo demo script
├── test_gitea_connection.py # Gitea connection tester
├── requirements.txt      # Dependencies
├── requirements-dev.txt  # Development dependencies
├── setup.py             # Package setup
├── .gitignore           # Git ignore rules
├── LICENSE              # MIT License
└── README.md            # This file
```

## 🚀 Advanced Usage

### Running MCP Server Separately

```bash
# Start the MCP server in background
python mcp/server.py &

# Run workflow with existing server
cd workflow
python main.py
```

### Programmatic Usage

```python
from workflow import WorkflowTrigger, GiteaMCPConnector, ContextBuilder, OpenAIModelInterface

# Initialize components
trigger = WorkflowTrigger()
connector = GiteaMCPConnector()
builder = ContextBuilder()
model = OpenAIModelInterface()

# Get user input
user_input = trigger.submit_issue("Forge/maestro,main,Add dark mode toggle")

# Fetch repository context
repo_context = connector.get_project_repository_context("Forge/maestro", "main")

# Build combined context
combined_context = builder.build_combined_context(user_input, repo_context)

# Generate plan
plan = model.generate_llm_template_and_send(combined_context)
```

### Custom Configuration

```bash
# Create custom config
cp config/config.sh my_config.sh
# Edit my_config.sh with your settings
source my_config.sh

# Run with custom config
cd workflow
python main.py
```

## 🔧 Troubleshooting

### Common Issues

1. **MCP Server Connection Failed**
   ```bash
   # Check if server is running
   curl http://localhost:8080/version

   # Start server manually
   python mcp/server.py
   ```

2. **Gitea Authentication Failed**
   ```bash
   # Verify token in config
   echo $GITEA_ACCESS_TOKEN

   # Test Gitea API access
   curl -H "Authorization: token $GITEA_ACCESS_TOKEN" $GITEA_URL/api/v1/user
   ```

3. **AI Model Connection Failed**
   ```bash
   # Test model server
   curl $OPENAI_API_BASE_URL/models

   # Check model availability
   curl -X POST $OPENAI_API_BASE_URL/chat/completions \
     -H "Content-Type: application/json" \
     -d '{"model":"your-model","messages":[{"role":"user","content":"test"}]}'
   ```

### Debug Mode

```bash
# Enable debug logging
export PYTHONPATH=.
cd workflow
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from main import main
main()
"
```

## 📊 Performance

- **File Caching**: Reduces API calls by 80-90% for repeated requests
- **Context Optimization**: Handles repositories up to 10,000 files efficiently
- **Async Operations**: MCP server supports concurrent requests
- **Memory Efficient**: Streaming file processing for large repositories

## 🔒 Security

This project follows security best practices:

- **🔐 Credential Protection**: All sensitive data in local config files (excluded from version control)
- **🛡️ Input Validation**: Sanitized user inputs and API responses
- **🌐 HTTPS Support**: SSL/TLS validation for secure connections
- **📁 Secure File Permissions**: Restricted access to configuration files
- **🚫 No Hardcoded Secrets**: No credentials committed to repository
- **⚡ Minimal Permissions**: API tokens require only necessary access rights
- **🔄 Regular Updates**: Dependencies monitored for security vulnerabilities

### Security Features

- Automatic `.gitignore` rules for sensitive files
- Cross-platform secure file permissions
- Environment variable validation
- SSL certificate verification
- Request timeout protection
- File size limits for uploads

**📖 For detailed security guidelines, see [docs/SECURITY.md](docs/SECURITY.md)**

## 🤝 Contributing

We welcome contributions! Please see our [Development Guide](docs/DEVELOPMENT.md) for details.

### Quick Start for Contributors

```bash
# Fork and clone the repository
git clone https://github.com/your-username/workflow.git
cd workflow

# Create virtual environment
python -m venv venv
source venv/bin/activate  # Linux/Mac
# or venv\Scripts\activate  # Windows

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
pytest tests/

# Format code
black .

# Submit pull request
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [LangChain](https://github.com/langchain-ai/langchain) for AI framework
- [MCP](https://github.com/modelcontextprotocol/python-sdk) for Model Context Protocol
- [Gitea](https://gitea.io/) for repository hosting
- [aiohttp](https://github.com/aio-libs/aiohttp) for async HTTP server
