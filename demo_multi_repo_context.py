#!/usr/bin/env python3
"""
Demo: Multi-Repository Context System
Demonstrates how to use the multi-repo context manager to create comprehensive
code context from multiple projects for AI-assisted development.
"""

import os
import sys
import argparse
from pathlib import Path

# Add integration to path
sys.path.insert(0, "integration")
from multi_repo_context_manager import MultiRepoContextManager, ProjectSource

def demo_basic_usage():
    """Demonstrate basic usage of the multi-repo context manager."""
    print("🚀 Multi-Repository Context Demo")
    print("=" * 50)
    
    # Initialize the context manager
    manager = MultiRepoContextManager()
    
    # Add current workflow project as target
    manager.add_local_project(
        name="workflow-system",
        path=".",
        description="Main workflow system with Gitea integration",
        priority=10  # Highest priority as target project
    )
    
    # Auto-discover projects in common directories
    common_dirs = [
        os.path.expanduser("~/Documents"),
        os.path.expanduser("~/Projects"),
        "/home/<USER>/Documents",  # Your specific path
        "."  # Current directory
    ]
    
    for base_dir in common_dirs:
        if os.path.exists(base_dir):
            print(f"🔍 Scanning {base_dir} for projects...")
            discovered = manager.scan_directory_for_projects(base_dir, max_depth=2)
            print(f"   Found {len(discovered)} projects")
            
            # Add discovered projects with lower priority
            for project in discovered[:5]:  # Limit to first 5 to avoid overwhelming
                project.priority = 3  # Lower priority than target
                manager.add_project_source(project)
    
    # Generate combined context
    print("\n📊 Generating combined context...")
    combined_context = manager.generate_combined_context(target_project="workflow-system")
    
    # Store for later use
    manager.combined_context = combined_context
    
    # Display summary
    stats = combined_context["combined_statistics"]
    print(f"\n✅ Context Generation Complete!")
    print(f"   📁 Projects: {stats['total_projects']}")
    print(f"   📄 Files: {stats['total_files']}")
    print(f"   💾 Total Size: {stats['total_size']:,} characters")
    print(f"   🔤 Languages: {', '.join(stats['languages_detected'])}")
    print(f"   📋 File Types: {len(stats['file_types'])} types")
    
    # Show project breakdown
    print(f"\n📋 Project Breakdown:")
    for project in combined_context["source_projects"]:
        print(f"   • {project['name']} ({project['type']}): {project['file_count']} files")
    
    # Export context in different formats
    print(f"\n💾 Exporting context...")
    
    # Create output directory
    output_dir = Path("multi_repo_context_output")
    output_dir.mkdir(exist_ok=True)
    
    # Export as markdown (good for review)
    markdown_file = manager.export_context_for_ai(
        str(output_dir / "context.md"), 
        format="markdown"
    )
    print(f"   📝 Markdown: {markdown_file}")
    
    # Export as JSON (good for programmatic use)
    json_file = manager.export_context_for_ai(
        str(output_dir / "context.json"), 
        format="json"
    )
    print(f"   📊 JSON: {json_file}")
    
    # Export as text (good for AI input)
    text_file = manager.export_context_for_ai(
        str(output_dir / "context.txt"), 
        format="text"
    )
    print(f"   📄 Text: {text_file}")
    
    print(f"\n🎯 Use Case Examples:")
    print(f"   1. Feed {text_file} to an AI model for code analysis")
    print(f"   2. Use {json_file} for programmatic context processing")
    print(f"   3. Review {markdown_file} to understand project patterns")
    
    # Cleanup
    manager.cleanup()
    
    return output_dir

def demo_targeted_analysis(target_repo: str, local_projects: list):
    """Demonstrate targeted analysis for a specific repository."""
    print(f"\n🎯 Targeted Analysis Demo")
    print(f"Target Repository: {target_repo}")
    print("=" * 50)
    
    manager = MultiRepoContextManager()
    
    # Add target repository (could be a Gitea clone)
    if os.path.exists(target_repo):
        manager.add_local_project(
            name="target-repo",
            path=target_repo,
            description="Target repository for enhancement",
            priority=10
        )
    
    # Add local projects for context
    for i, project_path in enumerate(local_projects):
        if os.path.exists(project_path):
            project_name = Path(project_path).name
            manager.add_local_project(
                name=f"context-{project_name}",
                path=project_path,
                description=f"Context project: {project_name}",
                priority=5 - i  # Decreasing priority
            )
    
    # Generate context focused on target
    combined_context = manager.generate_combined_context(target_project="target-repo")
    manager.combined_context = combined_context
    
    # Export for AI analysis
    output_file = f"targeted_analysis_{Path(target_repo).name}.txt"
    exported_file = manager.export_context_for_ai(output_file, format="text")
    
    print(f"✅ Targeted analysis exported to: {exported_file}")
    print(f"📊 Context includes {len(combined_context['source_projects'])} projects")
    
    # Show code examples found
    examples = combined_context["code_examples"]
    print(f"\n🔍 Code Examples Found:")
    for language, categories in examples.items():
        print(f"   {language}:")
        for category, example_list in categories.items():
            print(f"     • {category}: {len(example_list)} examples")
    
    manager.cleanup()
    return exported_file

def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(description="Multi-Repository Context Demo")
    parser.add_argument("--mode", choices=["basic", "targeted"], default="basic",
                       help="Demo mode to run")
    parser.add_argument("--target", type=str, help="Target repository path for targeted analysis")
    parser.add_argument("--projects", nargs="+", help="Local project paths for context")
    
    args = parser.parse_args()
    
    if args.mode == "basic":
        output_dir = demo_basic_usage()
        print(f"\n🎉 Demo completed! Check {output_dir} for exported context files.")
        
    elif args.mode == "targeted":
        if not args.target:
            print("❌ Target repository required for targeted analysis")
            print("Usage: python demo_multi_repo_context.py --mode targeted --target /path/to/repo")
            return
        
        local_projects = args.projects or []
        exported_file = demo_targeted_analysis(args.target, local_projects)
        print(f"\n🎉 Targeted analysis completed! Context exported to: {exported_file}")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Review the exported context files")
    print(f"   2. Feed the text file to your AI model")
    print(f"   3. Use the context to improve your target repository")
    print(f"   4. Integrate with your workflow system for automated analysis")

if __name__ == "__main__":
    main()
