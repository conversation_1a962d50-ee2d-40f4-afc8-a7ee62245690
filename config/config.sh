#!/bin/bash
# Workflow Configuration Template
# Copy this file to config/local_config.sh and edit with your actual values
# DO NOT commit local_config.sh to version control

# Gitea Configuration
export GITEA_URL="${GITEA_URL:-http://localhost:3000}"
export GITEA_ACCESS_TOKEN="${GITEA_ACCESS_TOKEN:-your_gitea_token_here}"

# MCP Server Configuration
export GITMCP_SERVER_URL="${GITMCP_SERVER_URL:-http://localhost:8080}"

# OpenAI-Compatible Server Configuration
export OPENAI_API_BASE_URL="${OPENAI_API_BASE_URL:-http://localhost:8001/v1}"
export OPENAI_API_KEY="${OPENAI_API_KEY:-}"

# Local Repository Clone Path (cross-platform compatible)
if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    export LOCAL_REPO_CLONE_PATH="${LOCAL_REPO_CLONE_PATH:-$TEMP/gitea_clones}"
else
    export LOCAL_REPO_CLONE_PATH="${LOCAL_REPO_CLONE_PATH:-/tmp/gitea_clones}"
fi

# File Cache Configuration
export FILE_CACHE_DIR="${FILE_CACHE_DIR:-$HOME/.workflow_cache}"
export FILE_CACHE_MAX_AGE="${FILE_CACHE_MAX_AGE:-3600}"

echo "✅ Workflow environment variables set."
echo "🔗 GITEA_URL=$GITEA_URL"
echo "🔗 GITMCP_SERVER_URL=$GITMCP_SERVER_URL"
echo "🔗 OPENAI_API_BASE_URL=$OPENAI_API_BASE_URL"
echo "📁 LOCAL_REPO_CLONE_PATH=$LOCAL_REPO_CLONE_PATH"
echo ""
echo "⚠️  IMPORTANT: Edit config/local_config.sh with your actual credentials"
echo "⚠️  DO NOT commit local_config.sh to version control"
