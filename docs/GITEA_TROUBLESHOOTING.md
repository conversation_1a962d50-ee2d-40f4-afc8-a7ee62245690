# Gitea Connection Troubleshooting Guide

## 🔍 Your Current Issue

**Status**: HTTP 401 - Authentication Failed  
**Error**: "user does not exist [uid: 0, name: ]"  
**Token**: MCP-Debug-Token-May20  

This indicates the token is being received by Gitea but is not valid for authentication.

## 🛠️ Step-by-Step Fix

### 1. **Verify Token in Gitea Web Interface**

1. Log into your Gitea web interface: `http://localhost:3000`
2. Go to **Settings** → **Applications** → **Manage Access Tokens**
3. Check if `MCP-Debug-Token-May20` is listed and active
4. Verify the token has the required permissions

### 2. **Check Token Permissions**

Your token needs these permissions:
- ✅ **repo** - Repository access
- ✅ **read:user** - Read user information
- ✅ **read:org** - Read organization information (if using orgs)

### 3. **Regenerate Access Token**

If the token is missing or expired:

1. In Gitea web interface, go to **Settings** → **Applications**
2. Click **Generate New Token**
3. **Token Name**: `Workflow-System-Token`
4. **Select Scopes**:
   - ✅ repo
   - ✅ read:user
   - ✅ read:org
   - ✅ read:repository
5. Click **Generate Token**
6. **Copy the new token immediately** (you won't see it again)

### 4. **Test New Token**

Use the test script with your new token:

```bash
python test_gitea_connection.py
```

Enter:
- **URL**: `http://localhost:3000`
- **Username**: `Admin`
- **Token**: `[your-new-token]`

## 🔧 Common Issues & Solutions

### Issue 1: Token Not Found
**Symptoms**: HTTP 401, "user does not exist"  
**Solution**: Regenerate token with proper permissions

### Issue 2: Wrong Username
**Symptoms**: HTTP 401, authentication failed  
**Solution**: Use exact username from Gitea (case-sensitive)

### Issue 3: Token Expired
**Symptoms**: HTTP 401, token invalid  
**Solution**: Generate new token (tokens can expire)

### Issue 4: Insufficient Permissions
**Symptoms**: HTTP 403, access denied  
**Solution**: Regenerate token with all required scopes

### Issue 5: Gitea Configuration
**Symptoms**: API endpoints not found  
**Solution**: Ensure Gitea API is enabled in configuration

## 🧪 Manual Testing

### Test 1: Basic Gitea Access
```bash
curl http://localhost:3000
```
Should return Gitea homepage HTML.

### Test 2: API Endpoint
```bash
curl http://localhost:3000/api/v1/version
```
Should return Gitea version information.

### Test 3: Authentication
```bash
curl -H "Authorization: token YOUR_TOKEN" http://localhost:3000/api/v1/user
```
Should return your user information.

## 📋 Token Generation Checklist

When creating a new access token:

- [ ] **Logged in as correct user** (Admin)
- [ ] **Token name is descriptive** (e.g., "Workflow-System")
- [ ] **Selected required scopes**:
  - [ ] repo
  - [ ] read:user
  - [ ] read:org
  - [ ] read:repository
- [ ] **Copied token immediately** after generation
- [ ] **Token stored securely**
- [ ] **Tested token with curl or test script**

## 🔄 Quick Fix Steps

1. **Generate New Token**:
   - Go to Gitea → Settings → Applications
   - Generate new token with `repo` and `read:user` permissions
   - Copy the token

2. **Test Connection**:
   ```bash
   python test_gitea_connection.py
   ```

3. **Use in Unified GUI**:
   - Launch: `python launch_unified_gui.py`
   - Go to Source Selection tab
   - Click "Connect to Gitea"
   - Enter your new token

## 🎯 Expected Success Output

When the connection works, you should see:

```
✅ SUCCESS! Connected as: Admin
User ID: 1
Email: <EMAIL>
```

## 🆘 Still Having Issues?

### Check Gitea Logs
```bash
# If running Gitea with Docker
docker logs gitea

# If running Gitea directly
tail -f /path/to/gitea/logs/gitea.log
```

### Verify Gitea Configuration
Check that API access is enabled in `app.ini`:
```ini
[api]
ENABLE_SWAGGER = true
MAX_RESPONSE_ITEMS = 50
```

### Network Issues
- Ensure Gitea is accessible: `telnet localhost 3000`
- Check firewall settings
- Verify port 3000 is not blocked

## 📞 Next Steps

1. **Generate a new access token** with proper permissions
2. **Test with the test script** to verify it works
3. **Use the new token in the unified GUI**
4. **Proceed with repository analysis and workflow generation**

Once the token is working, you'll be able to:
- ✅ Browse your Gitea repositories
- ✅ Select branches dynamically
- ✅ Clone repositories for analysis
- ✅ Generate shotgun-enhanced context
- ✅ Create AI-powered implementation plans

The unified workflow system is ready - you just need a valid access token! 🚀
